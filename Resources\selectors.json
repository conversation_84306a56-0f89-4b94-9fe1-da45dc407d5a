{"username_field": "input[name='userName']", "username_next_button": "body > div:nth-child(1) > div.featurette-page > div:nth-child(1) > div > div.adl-grid__cell.adl-grid__cell--width-12-of-12.b-margin-top-070 > form > div > div.b-form-layout__actions-container > div:nth-child(1) > button > span", "password_field": "input[data-validation-name='password']", "password_next_button": "body > div:nth-child(1) > div.featurette-page > div:nth-child(1) > div > div.adl-grid__cell.adl-grid__cell--width-12-of-12.b-margin-top-070 > form > div.b-form-layout > div.b-form-layout__actions-container > div:nth-child(1) > button.b-button.b-loading-button.b-button--primary > span", "otp_continue_button": "button[id='otp-continue']", "otp_input_field": "input[name='totpCode']", "otp_submit_button": "body > div:nth-child(1) > div.featurette-page > div:nth-child(1) > div > div.adl-grid__cell.adl-grid__cell--width-12-of-12.b-margin-top-070 > form > div > div.b-form-layout__actions-container > div:nth-child(1) > button.b-button.b-loading-button.b-button--primary > span", "dropdown": "div.selected-merchant.selected-merchant--switchable", "search_box": "input.input", "second_dropdown": "button[class*='app-navigation-button'][class*='app-navigation-expand-button']", "second_dropdown_option": "[title='Aggregate settlement']", "manage_report_button": "button:has-text('Manage Report')", "manual_generate_option": "button:has-text('Manual (generate once)')", "date_range_selector": "div.adl-field-layout__field.adl-field.u-width-full", "from_date_input": "input[placeholder='yyyy-mm-dd']", "to_date_input": "input[placeholder='yyyy-mm-dd']", "submit_button": "button.panel__submit-button.adl-button.u-margin-left-16", "radio_button_1": "input[type='radio'][data-value='true']", "radio_button_2": "input[type='radio'][data-value='true']", "file_type_dropdown": "select.adl-selectbox__select", "generate_report_button": "button:has-text('Generate report')", "download_icon": "td.adl-table__cell.u-text-align-right a span i.adl-icon-download"}