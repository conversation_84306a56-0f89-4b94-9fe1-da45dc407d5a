{"username_field": "input[name='userName']", "username_next_button": "body > div:nth-child(1) > div.featurette-page > div:nth-child(1) > div > div.adl-grid__cell.adl-grid__cell--width-12-of-12.b-margin-top-070 > form > div > div.b-form-layout__actions-container > div:nth-child(1) > button > span", "password_field": "input[data-validation-name='password']", "password_next_button": "body > div:nth-child(1) > div.featurette-page > div:nth-child(1) > div > div.adl-grid__cell.adl-grid__cell--width-12-of-12.b-margin-top-070 > form > div.b-form-layout > div.b-form-layout__actions-container > div:nth-child(1) > button.b-button.b-loading-button.b-button--primary > span", "otp_continue_button": "button[id='otp-continue']", "otp_input_field": "input[name='totpCode']", "otp_submit_button": "body > div:nth-child(1) > div.featurette-page > div:nth-child(1) > div > div.adl-grid__cell.adl-grid__cell--width-12-of-12.b-margin-top-070 > form > div > div.b-form-layout__actions-container > div:nth-child(1) > button.b-button.b-loading-button.b-button--primary > span", "dropdown": "div.selected-merchant.selected-merchant--switchable", "search_box": "input.input", "second_dropdown": "button[class*='app-navigation-button'][class*='app-navigation-expand-button']", "second_dropdown_option": "[title='Aggregate settlement']", "manage_report_button": "button:has-text('Manage Report')", "manual_generate_option": "button:has-text('Manual (generate once)')", "date_range_selector": "div.adl-field-layout__field.adl-field.u-width-full", "from_date_input": "input[placeholder='yyyy-mm-dd']", "to_date_input": "input[placeholder='yyyy-mm-dd']", "submit_button": "button.panel__submit-button.adl-button.u-margin-left-16", "radio_button_1": "input[type='radio'][data-value='true']", "radio_button_2": "input[type='radio'][data-value='true']", "file_type_dropdown": "select.adl-selectbox__select", "generate_report_button": "button:has-text('Generate report')", "download_icon": "td.adl-table__cell.u-text-align-right a span i.adl-icon-download", "username_field_ezxater": "input[name='contact[username]']", "continue_button_ezxater": "#new_contact > input.btn.submit-button", "password_field_ezxater": "input#password", "sign_in_button": "body > div.c0e8e34f5.c3e345b1a > main > section > div > div > div > form > div.cbc7f6475 > button", "financials_button": "div[data-sidebar=\"group-content\"] li[data-sidebar=\"menu-item\"]", "payments_table": "#root > div > div > div > div.min-w-0.max-w-full.flex-1.w-full.h-screen > div > div > div.c-xlKfJ.c-xlKfJ-jBgSWv-backgroundColor-gray > div > div > div.emotion-18h2lw4.e1b7eodf0 > div.dimmable > table[data-test=\"PaymentsTable\"]", "next_page_button": "div[aria-label=\"Pagination Navigation\"] a[aria-label=\"Next item\"]", "current_page": "div[aria-label=\"Pagination Navigation\"] a.item[tabindex=\"0\"]", "download_order_details_button": "#root > div > div > div > div.min-w-0.max-w-full.flex-1.w-full.h-screen > div > div > div.c-xlKfJ.c-xlKfJ-jBgSWv-backgroundColor-gray > div > div > div:nth-child(2) > div > div.c-dhzjXW > div > div:nth-child(2) > button", "download_my_report_button": "#root > div > div > div > div.min-w-0.max-w-full.flex-1.w-full.h-screen > div > div > div.c-xlKfJ.c-xlKfJ-jBgSWv-backgroundColor-gray > div > div > div:nth-child(2) > div > div.c-dhzjXW > div > div:nth-child(2) > a", "download_order_details_fallback": "button:has-text('Download Order Details')", "download_my_report_fallback": "a:has-text('Download my report')", "view_details_button": "td:nth-child(5)"}