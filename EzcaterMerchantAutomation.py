import argparse
import os
import sys
import csv
import re
from playwright.sync_api import sync_playwright, TimeoutError
import tkinter as tk
from tkinter import messagebox
from datetime import datetime, timedelta
import time
from main import setup_logger, load_selectors, load_email_config, send_email

logger, LOG_FILE = setup_logger()

class PlaywrightHelper:
    """Handles browser setup and common actions."""
    def __init__(self, bHeadless=False, storage_state=None, browser_type="chromium"):
        """Initialize Playwright browser with optional storage state and browser type."""
        self.playwright = None
        self.browser = None
        self.context = None
        self.objPage = None
        self.headless = bHeadless
        self.storage_state = storage_state
        self.browser_type = browser_type.lower()

    def start_browser(self, browser_args=None):
        """Launch specified browser (Chromium or Firefox) with arguments."""
        try:
            self.playwright = sync_playwright().start()
            browser_args = browser_args or ["--start-maximized"]
            if self.browser_type == "firefox":
                self.browser = self.playwright.firefox.launch(headless=self.headless, args=browser_args)
                logger.info("Browser launched successfully (Firefox).")
            else:
                self.browser = self.playwright.chromium.launch(headless=self.headless, args=browser_args)
                logger.info("Browser launched successfully (Chromium).")
            self.context = self.browser.new_context(
                no_viewport=True,
                storage_state=self.storage_state if self.storage_state else None
            )
            self.objPage = self.context.new_page()
            self.objPage.on("pageerror", lambda error: logger.warning(f"Page error (non-critical, {self.browser_type}): {str(error)}"))
        except Exception as e:
            logger.error(f"Failed to launch browser ({self.browser_type}): {str(e)}", exc_info=True)
            raise

    def login(self, url, username, password, selectors):
        """Perform login steps with URL change handling."""
        try:
            self.objPage.goto(url)
            logger.info(f"Navigated to login page: {url} ({self.browser_type})")
            username_locator = self.objPage.locator(selectors["username_field_ezxater"])
            username_locator.wait_for(state="visible", timeout=30000)
            username_locator.fill(username)
            logger.info(f"Username entered: {username} ({self.browser_type})")
            continue_button = self.objPage.locator(selectors["continue_button_ezxater"])
            continue_button.wait_for(state="visible", timeout=30000)
            with self.objPage.expect_navigation(timeout=60000):
                continue_button.click()
            logger.info(f"Clicked Continue button and navigated to new URL: {self.objPage.url} ({self.browser_type})")
            password_locator = self.objPage.locator(selectors["password_field_ezxater"])
            password_locator.wait_for(state="visible", timeout=30000)
            password_locator.fill(password)
            logger.info(f"Password entered ({self.browser_type})")
            sign_in_button = self.objPage.locator(selectors["sign_in_button"])
            sign_in_button.wait_for(state="visible", timeout=30000)
            with self.objPage.expect_navigation(timeout=60000):
                sign_in_button.click()
            logger.info(f"Clicked Sign In button ({self.browser_type})")
            redirected_url = self.objPage.url
            if "ezmanage.ezcater.com" in redirected_url.lower() and "login" not in redirected_url.lower():
                logger.info(f"Login successful, redirected to: {redirected_url} ({self.browser_type})")
                return redirected_url
            else:
                logger.error(f"Login failed: Redirected to unexpected URL: {redirected_url} ({self.browser_type})")
                raise ValueError("Login failed: Redirected to unexpected URL")
        except TimeoutError as e:
            logger.error(f"Timeout during login process: {str(e)} ({self.browser_type})", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"Error during login: {str(e)} ({self.browser_type})", exc_info=True)
            raise

    def save_storage_state(self, path):
        """Save the browser's storage state to a file."""
        try:
            self.context.storage_state(path=path)
            logger.info(f"Storage state saved to {path} ({self.browser_type})")
        except Exception as e:
            logger.error(f"Failed to save storage state ({self.browser_type}): {str(e)}", exc_info=True)
            raise

    def close(self):
        """Close browser and Playwright instance."""
        try:
            if self.context:
                self.context.close()
                self.context = None
            if self.browser:
                self.browser.close()
                self.browser = None
            if self.playwright:
                self.playwright.stop()
                self.playwright = None
            logger.info(f"Browser closed successfully ({self.browser_type}).")
        except Exception as e:
            logger.error(f"Error closing browser ({self.browser_type}): {str(e)}", exc_info=True)

class CWebNavigator:
    """Handles the login workflow and post-login actions."""
    def __init__(self, playwright_instance):
        """Initialize with PlaywrightHelper instance."""
        self.playwright = playwright_instance
        self.objPage = self.playwright.objPage
        self.browser_type = self.playwright.browser_type
        self.redirected_url = None
        self.downloaded_files = []
        self.download_stats = []

    def show_error_popup(self, error_message="An error occurred during the process."):
        """Display a Tkinter pop-up to notify the user of an error."""
        try:
            root = tk.Tk()
            root.withdraw()
            root.attributes('-topmost', True)
            logger.info(f"Displaying Tkinter error pop-up ({self.browser_type}).")
            messagebox.showerror(
                title="EZManage Login - Error",
                message=f"{error_message} Please check the logs for details or contact support.",
                parent=root
            )
            logger.info(f"Tkinter error pop-up OK button clicked ({self.browser_type}).")
            root.destroy()
        except Exception as e:
            logger.error(f"Error in Tkinter error pop-up ({self.browser_type}): {str(e)}", exc_info=True)
            raise

    def handle_login(self, url, username, password, selectors):
        """Handle the login process and capture the redirected URL."""
        try:
            self.redirected_url = self.playwright.login(url, username, password, selectors)
            logger.info(f"Login successful, redirected URL: {self.redirected_url} ({self.browser_type})")
            return True
        except Exception as e:
            logger.error(f"Login failed: {str(e)} ({self.browser_type})", exc_info=True)
            self.show_error_popup("Login failed")
            return False

    def post_login_actions(self, selectors, from_date=None, to_date=None):
        """Perform post-login actions, downloading reports for the specified or last month's date range."""
        try:
            # Navigate to Financials page
            financials_button = self.objPage.locator(selectors["financials_button"]).filter(has=self.objPage.locator("span").filter(has_text="Financials"))
            financials_button.wait_for(state="visible", timeout=60000)
            with self.objPage.expect_navigation(timeout=60000):
                financials_button.click()
            logger.info(f"Navigated to Financials page: {self.objPage.url} ({self.browser_type})")
            financials_url = self.objPage.url  # Store Financials page URL for navigation

            # Determine date range for report filtering
            if from_date and to_date:
                try:
                    start_date = datetime.strptime(from_date, "%m/%d/%y")
                    end_date = datetime.strptime(to_date, "%m/%d/%y")
                    if end_date < start_date:
                        logger.error(f"Invalid date range: to_date ({to_date}) is before from_date ({from_date})")
                        self.show_error_popup("Invalid date range: to_date cannot be before from_date")
                        return False
                    logger.info(f"Using custom date range: {from_date} to {to_date}")
                except ValueError as e:
                    logger.error(f"Invalid date format for from_date ({from_date}) or to_date ({to_date}): {str(e)}")
                    self.show_error_popup(f"Invalid date format for from_date or to_date. Use mm/dd/yy format.")
                    return False
            else:
                # Default: Last month
                current_date = datetime.now()
                last_month = current_date.replace(day=1) - timedelta(days=1)
                start_date = last_month.replace(day=1)
                end_date = last_month
                logger.info(f"Using default date range: {start_date.strftime('%m/%d/%y')} to {end_date.strftime('%m/%d/%y')}")

            # Determine stop date (previous-to-last month start)
            stop_date = (start_date.replace(day=1) - timedelta(days=1)).replace(day=1)
            logger.info(f"Filtering for reports from {start_date.strftime('%m/%d/%y')} to {end_date.strftime('%m/%d/%y')}, stopping if date <= {stop_date.strftime('%m/%d/%y')}")

            # Prepare CSV file for tracking downloaded reports
            csv_file = os.path.join(os.path.dirname(__file__), "downloaded_reports.csv")
            headers = ["Sent On", "Method", "Recipient", "Amount", "Action", "Downloaded File"]
            downloaded_reports = set()
            if os.path.exists(csv_file):
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    next(reader, None)  # Skip header
                    for row in reader:
                        if row and len(row) >= 4:
                            downloaded_reports.add((row[0], row[2], row[3]))
            with open(csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                if not downloaded_reports:
                    writer.writerow(headers)
            logger.info(f"Initialized CSV for tracking downloads: {csv_file}")

            # Create reports directory
            reports_dir = os.path.join(os.path.dirname(__file__), "reports")
            os.makedirs(reports_dir, exist_ok=True)
            logger.info(f"Reports will be saved to: {reports_dir}")

            current_page = 1
            while True:
                logger.info(f"Processing page {current_page} ({self.browser_type})")
                # Wait for the table to be visible
                try:
                    table = self.objPage.locator(selectors["payments_table"])
                    table.wait_for(state="visible", timeout=90000)
                    if not table.is_visible():
                        logger.warning(f"Payments table not found on page {current_page}, trying fallback locator")
                        table = self.objPage.locator("table tr")  # Fallback to any table rows
                        table.wait_for(state="visible", timeout=90000)
                except TimeoutError:
                    logger.warning(f"Table not visible on page {current_page}")
                    next_button = self.objPage.locator(selectors["next_page_button"])
                    if not next_button.is_visible() or not next_button.is_enabled():
                        logger.info(f"No more pages after page {current_page}, ending process")
                        break
                    next_button.click()
                    current_page += 1
                    logger.info(f"Navigated to page {current_page}: {self.objPage.url} ({self.browser_type})")
                    self.objPage.wait_for_load_state("domcontentloaded", timeout=90000)
                    time.sleep(3)
                    continue

                rows = table.locator("tr").all()
                if not rows:
                    logger.warning(f"No rows found in the payments table on page {current_page}")
                    next_button = self.objPage.locator(selectors["next_page_button"])
                    if not next_button.is_visible() or not next_button.is_enabled():
                        logger.info(f"No more pages after page {current_page}, ending process")
                        break
                    next_button.click()
                    current_page += 1
                    logger.info(f"Navigated to page {current_page}: {self.objPage.url} ({self.browser_type})")
                    self.objPage.wait_for_load_state("domcontentloaded", timeout=90000)
                    time.sleep(3)
                    continue

                report_processed = False
                for row in rows:
                    try:
                        date_cell = row.locator("td:nth-child(1)")
                        date_cell.wait_for(state="visible", timeout=60000)
                        date_text = date_cell.inner_text().strip()
                        if not date_text:
                            logger.warning(f"Empty date in row on page {current_page}")
                            continue
                        try:
                            report_date = datetime.strptime(date_text, "%m/%d/%y")
                        except ValueError:
                            logger.warning(f"Invalid date format in row on page {current_page}: {date_text}")
                            continue

                        # Stop if report is before the start date
                        if report_date < start_date:
                            logger.info(f"Detected report from {date_text}, earlier than start date. Stopping extraction.")
                            return True

                        # Process only reports within the specified range
                        if start_date <= report_date <= end_date:
                            row_data = [
                                date_text,
                                row.locator("td:nth-child(2)").inner_text().strip(),
                                row.locator("td:nth-child(3)").inner_text().strip(),
                                row.locator("td:nth-child(4)").inner_text().strip(),
                                row.locator("td:nth-child(5)").inner_text().strip() or "Button Present"
                            ]
                            report_tuple = (row_data[0], row_data[2], row_data[3])
                            if report_tuple in downloaded_reports:
                                logger.info(f"Skipping duplicate report for {date_text} with Recipient: {row_data[2]}, Amount: {row_data[3]}")
                                continue
                            logger.info(f"Processing report for {date_text} with Recipient: {row_data[2]}")
                            recipient = row_data[2]
                            recipient_sanitized = re.sub(r'[^\w\-]', '_', recipient.strip())
                            file_path = os.path.join(reports_dir, f"report_{date_text.replace('/', '-')}_{recipient_sanitized}.xlsx")
                            try:
                                view_details = row.locator("td:nth-child(5)")
                                view_details.wait_for(state="visible", timeout=60000)
                                time.sleep(2)
                                with self.objPage.expect_navigation(timeout=120000):
                                    view_details.click()
                                logger.info(f"Clicked View Details for report on {date_text}")
                                self.objPage.wait_for_load_state("domcontentloaded", timeout=120000)
                                logger.info(f"Landed on report page for {date_text}: {self.objPage.url}")
                                self.objPage.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                                time.sleep(1)
                                download_order_details = self.objPage.locator(selectors["download_order_details_button"])
                                download_my_report = self.objPage.locator(selectors["download_my_report_button"])
                                logger.info(f"Download Order Details count: {download_order_details.count()}, Download my report count: {download_my_report.count()}")
                                if download_order_details.count() > 0:
                                    download_order_details.scroll_into_view_if_needed()
                                    download_order_details.click()
                                    logger.info(f"Clicked Download Order Details for {date_text}")
                                    try:
                                        download_my_report.wait_for(state="attached", timeout=90000)
                                        href = download_my_report.get_attribute("href") or "N/A"
                                        logger.info(f"Download my report link href: {href}")
                                        with self.objPage.expect_download(timeout=60000) as download_info:
                                            download_my_report.scroll_into_view_if_needed()
                                            download_my_report.click()
                                        logger.info(f"Clicked Download my report link for {date_text}")
                                        download = download_info.value
                                        download.save_as(file_path)
                                        logger.info(f"Downloaded report for {date_text} to {file_path}")
                                    except TimeoutError:
                                        logger.warning(f"Primary Download my report selector timed out, trying fallback")
                                        download_my_report = self.objPage.locator(selectors["download_my_report_fallback"])
                                        download_my_report.wait_for(state="attached", timeout=90000)
                                        href = download_my_report.get_attribute("href") or "N/A"
                                        logger.info(f"Fallback Download my report link href: {href}")
                                        with self.objPage.expect_download(timeout=60000) as download_info:
                                            download_my_report.scroll_into_view_if_needed()
                                            download_my_report.click()
                                        logger.info(f"Clicked fallback Download my report link for {date_text}")
                                        download = download_info.value
                                        download.save_as(file_path)
                                        logger.info(f"Downloaded report for {date_text} to {file_path}")
                                elif download_my_report.count() > 0:
                                    download_my_report.scroll_into_view_if_needed()
                                    href = download_my_report.get_attribute("href") or "N/A"
                                    logger.info(f"Direct Download my report link href: {href}")
                                    with self.objPage.expect_download(timeout=60000) as download_info:
                                        download_my_report.click()
                                    logger.info(f"Clicked Download my report link for {date_text}")
                                    download = download_info.value
                                    download.save_as(file_path)
                                    logger.info(f"Downloaded report for {date_text} to {file_path}")
                                else:
                                    logger.warning(f"Primary selectors failed, trying fallback selectors")
                                    download_order_details = self.objPage.locator(selectors["download_order_details_fallback"])
                                    download_my_report = self.objPage.locator(selectors["download_my_report_fallback"])
                                    logger.info(f"Fallback Download Order Details count: {download_order_details.count()}, Fallback Download my report count: {download_my_report.count()}")
                                    if download_order_details.count() > 0:
                                        download_order_details.scroll_into_view_if_needed()
                                        download_order_details.click()
                                        logger.info(f"Clicked fallback Download Order Details for {date_text}")
                                        download_my_report.wait_for(state="attached", timeout=90000)
                                        href = download_my_report.get_attribute("href") or "N/A"
                                        logger.info(f"Fallback Download my report link href: {href}")
                                        with self.objPage.expect_download(timeout=60000) as download_info:
                                            download_my_report.scroll_into_view_if_needed()
                                            download_my_report.click()
                                        logger.info(f"Clicked fallback Download my report link for {date_text}")
                                        download = download_info.value
                                        download.save_as(file_path)
                                        logger.info(f"Downloaded report for {date_text} to {file_path}")
                                    elif download_my_report.count() > 0:
                                        download_my_report.scroll_into_view_if_needed()
                                        href = download_my_report.get_attribute("href") or "N/A"
                                        logger.info(f"Direct fallback Download my report link href: {href}")
                                        with self.objPage.expect_download(timeout=60000) as download_info:
                                            download_my_report.click()
                                        logger.info(f"Clicked fallback Download my report link for {date_text}")
                                        download = download_info.value
                                        download.save_as(file_path)
                                        logger.info(f"Downloaded report for {date_text} to {file_path}")
                                    else:
                                        logger.error(f"No download button or link found for {date_text}")
                                        self.show_error_popup(f"No download button or link found for report on {date_text}")
                                        self.download_stats.append({
                                            "date": date_text,
                                            "recipient": row_data[2],
                                            "amount": row_data[3],
                                            "file_path": None,
                                            "status": "Failed (No Download Button)",
                                            "error": "Download button not found"
                                        })
                                        self.objPage.goto(financials_url)
                                        self.objPage.wait_for_load_state("domcontentloaded", timeout=90000)
                                        logger.info(f"Navigated back to Financials page and reset to page 1: {self.objPage.url} ({self.browser_type})")
                                        current_page = 1
                                        break
                                # Record download in CSV and tracking
                                with open(csv_file, 'a', newline='', encoding='utf-8') as f:
                                    writer = csv.writer(f)
                                    writer.writerow(row_data + [file_path])
                                    downloaded_reports.add(report_tuple)
                                self.downloaded_files.append(file_path)
                                self.download_stats.append({
                                    "date": date_text,
                                    "recipient": row_data[2],
                                    "amount": row_data[3],
                                    "file_path": file_path,
                                    "status": "Success"
                                })
                                logger.info(f"Recorded download for {date_text} in CSV")
                                report_processed = True
                                self.objPage.goto(financials_url)
                                self.objPage.wait_for_load_state("domcontentloaded", timeout=90000)
                                logger.info(f"Navigated back to Financials page and reset to page 1: {self.objPage.url} ({self.browser_type})")
                                current_page = 1
                                break
                            except TimeoutError as e:
                                logger.error(f"Timeout during download process for {date_text}: {str(e)}", exc_info=True)
                                self.show_error_popup(f"Timeout during download for {date_text}")
                                self.download_stats.append({
                                    "date": date_text,
                                    "recipient": row_data[2],
                                    "amount": row_data[3],
                                    "file_path": None,
                                    "status": "Failed (Timeout)",
                                    "error": str(e)
                                })
                                self.objPage.goto(financials_url)
                                self.objPage.wait_for_load_state("domcontentloaded", timeout=90000)
                                logger.info(f"Navigated back to Financials page and reset to page 1: {self.objPage.url} ({self.browser_type})")
                                current_page = 1
                                break
                            except Exception as e:
                                logger.error(f"Error downloading report for {date_text}: {str(e)}", exc_info=True)
                                self.show_error_popup(f"Error downloading report for {date_text}")
                                self.download_stats.append({
                                    "date": date_text,
                                    "recipient": row_data[2],
                                    "amount": row_data[3],
                                    "file_path": None,
                                    "status": "Failed (Download Error)",
                                    "error": str(e)
                                })
                                self.objPage.goto(financials_url)
                                self.objPage.wait_for_load_state("domcontentloaded", timeout=90000)
                                logger.info(f"Navigated back to Financials page and reset to page 1: {self.objPage.url} ({self.browser_type})")
                                current_page = 1
                                break
                        else:
                            logger.info(f"Skipping report from {date_text} (not in specified date range)")
                    except TimeoutError:
                        logger.warning(f"Timeout accessing date in row on page {current_page}")
                        continue
                    except Exception as e:
                        logger.warning(f"Error accessing date in row on page {current_page}: {str(e)}", exc_info=True)
                        continue

                if report_processed:
                    continue

                next_button = self.objPage.locator(selectors["next_page_button"])
                logger.debug(f"Page {current_page}: Next button visibility: {next_button.is_visible()}, enabled: {next_button.is_enabled()}")
                if not next_button.is_visible() or not next_button.is_enabled():
                    logger.info(f"No more pages after page {current_page}, ending process")
                    break
                next_button.click()
                current_page += 1
                logger.info(f"Navigated to page {current_page}: {self.objPage.url} ({self.browser_type})")
                self.objPage.wait_for_load_state("domcontentloaded", timeout=90000)
                time.sleep(3)
            return True
        except TimeoutError as e:
            logger.error(f"Timeout during post-login actions: {str(e)} ({self.browser_type})", exc_info=True)
            self.show_error_popup("Failed to complete post-login actions")
            return False
        except Exception as e:
            logger.error(f"Error during post-login actions: {str(e)} ({self.browser_type})", exc_info=True)
            self.show_error_popup("Failed to complete post-login actions")
            return False

def setup_playwright_driver(driver_path, browser_type="chromium"):
    """Download Playwright browser drivers to a specific directory."""
    os.makedirs(driver_path, exist_ok=True)
    os.environ["PLAYWRIGHT_BROWSERS_PATH"] = driver_path
    with sync_playwright() as p:
        if browser_type.lower() == "firefox":
            p.firefox.launch()
            logger.info(f"Playwright Firefox drivers downloaded to: {driver_path}")
        else:
            p.chromium.launch()
            logger.info(f"Playwright Chromium drivers downloaded to: {driver_path}")
    return driver_path

def get_exe_directory():
    """Return the directory of the executable or script."""
    if getattr(sys, 'frozen', False):
        return os.path.dirname(sys.executable)
    return os.path.dirname(os.path.abspath(__file__))

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='EZManage Login Automation Script')
    parser.add_argument('--site-url', type=str, default='https://ezmanage.ezcater.com/', help='Login page URL')
    parser.add_argument('--username', type=str, default='<EMAIL>', help='Username for login')
    parser.add_argument('--password', type=str, default='Freshfoods@2023', help='Password for login')
    parser.add_argument('--driver-dir', type=str, help='Directory for Playwright drivers')
    parser.add_argument('--headless', type=bool, default=False, help='Run in headless mode')
    parser.add_argument('--browser-args', type=str, nargs='*', help='Custom browser arguments')
    parser.add_argument('--browser-type', type=str, default='chromium', choices=['chromium', 'firefox'], help='Browser type to use (chromium or firefox, default: chromium)')
    parser.add_argument('--state-file', type=str, default='state.json', help='Path to storage state file')
    parser.add_argument('--from-date', type=str ,help='Start date for report filtering (mm/dd/yy)')
    parser.add_argument('--to-date', type=str, help='End date for report filtering (mm/dd/yy)')
    return parser.parse_args()

def is_session_valid(page, site_url, browser_type, selectors):
    """Check if the stored session is valid by navigating to the dashboard URL."""
    try:
        page.goto(site_url)
        logger.info(f"Navigated to site URL: {site_url} ({browser_type})")
        page.wait_for_load_state("domcontentloaded", timeout=30000)
        if "ezmanage.ezcater.com" in page.url.lower() and "login" not in page.url.lower():
            logger.info(f"Stored session is valid: Dashboard URL confirmed: {page.url} ({browser_type})")
            return True
        if page.locator(selectors["username_field_ezxater"]).count() > 0 or "login" in page.url.lower():
            logger.info(f"Stored session is invalid: Redirected to login page ({browser_type}).")
            if os.path.exists(f"state_{browser_type}.json"):
                os.remove(f"state_{browser_type}.json")
                logger.info(f"Deleted invalid state_{browser_type}.json to force fresh login.")
            return False
        logger.warning(f"Session validation failed: Unexpected URL: {page.url} ({browser_type}).")
        return False
    except Exception as e:
        logger.error(f"Error checking session validity: {str(e)} ({browser_type})")
        return False

def handle_full_login(navigator, playwright_helper, site_url, username, password, state_file, selectors):
    """Handle full login process when no state.json or session is invalid."""
    try:
        logger.info(f"Initiating full login process ({playwright_helper.browser_type})...")
        success = navigator.handle_login(site_url, username, password, selectors)
        if success:
            logger.info(f"Login automation completed successfully ({playwright_helper.browser_type}).")
            playwright_helper.save_storage_state(state_file)
            return True
        else:
            logger.error(f"Login automation failed ({playwright_helper.browser_type}).")
            navigator.show_error_popup("Login failed")
            return False
    except Exception as e:
        logger.error(f"Error in full login process: {str(e)} ({playwright_helper.browser_type})", exc_info=True)
        navigator.show_error_popup("Error during login process")
        return False

def handle_session_validation(navigator, playwright_helper, site_url, username, password, state_file, selectors):
    """Validate session with state.json, fall back to full login if invalid."""
    try:
        is_valid = is_session_valid(playwright_helper.objPage, site_url, playwright_helper.browser_type, selectors)
        if is_valid:
            logger.info(f"Using stored authentication state. Skipping login ({playwright_helper.browser_type}).")
            navigator.redirected_url = playwright_helper.objPage.url
            return True
        else:
            logger.warning(f"Stored session invalid or expired. Falling back to full login ({playwright_helper.browser_type}).")
            return handle_full_login(navigator, playwright_helper, site_url, username, password, state_file, selectors)
    except Exception as e:
        logger.error(f"Error in session validation process: {str(e)} ({playwright_helper.browser_type})", exc_info=True)
        navigator.show_error_popup("Error during session validation")
        return False

def send_notification_email(success, downloaded_files, download_stats, total_time, browser_type, email_config, username):
    """Send email notification with EZCater automation statistics and CSV file attachment."""
    status = "Success" if success else "Failed"
    manual_time_minutes = 120  # Estimated 2 hours for manual EZCater report downloads
    total_time_minutes = total_time / 60
    time_saved_minutes = manual_time_minutes - total_time_minutes
    total_reports_processed = len(download_stats)
    successful_downloads = len([stat for stat in download_stats if stat["status"] == "Success"])
    failed_downloads = total_reports_processed - successful_downloads
    avg_time_per_report = (total_time / total_reports_processed) if total_reports_processed > 0 else 0
    unique_recipients = len(set(stat["recipient"] for stat in download_stats)) if download_stats else 0
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
    <style>
    body {{ font-family: Arial, sans-serif; margin: 20px; color: #333; }}
    .header {{ background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 5px solid #1976d2; }}
    .summary {{ background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
    .stats-table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; font-size: 14px; }}
    .stats-table th, .stats-table td {{ border: 1px solid #ddd; padding: 10px; text-align: left; }}
    .stats-table th {{ background-color: #1976d2; color: white; font-weight: bold; }}
    .stats-table tr:nth-child(even) {{ background-color: #f9f9f9; }}
    .status-success {{ color: #2e7d32; font-weight: bold; }}
    .status-failed {{ color: #d32f2f; font-weight: bold; }}
    .highlight {{ background-color: #fff3cd; padding: 10px; border-radius: 5px; }}
    .metric {{ display: inline-block; margin: 10px 15px 10px 0; }}
    .metric-value {{ font-size: 18px; font-weight: bold; color: #1976d2; }}
    .metric-label {{ font-size: 12px; color: #666; }}
    </style>
    </head>
    <body>
    <div class="header">
        <h2>🍽️ EZCater Report Downloader - Automation {status}</h2>
        <p><strong>Executed by:</strong> {username}</p>
        <p><strong>Browser:</strong> {browser_type.title()}</p>
        <p><strong>Execution Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>Report Period:</strong> Last Month's Financial Reports</p>
    </div>
    <div class="summary">
        <h3>📊 Automation Summary</h3>
        <div class="metric">
            <div class="metric-value">{status}</div>
            <div class="metric-label">Overall Status</div>
        </div>
        <div class="metric">
            <div class="metric-value">{total_reports_processed}</div>
            <div class="metric-label">Reports Processed</div>
        </div>
        <div class="metric">
            <div class="metric-value">{successful_downloads}</div>
            <div class="metric-label">Successful Downloads</div>
        </div>
        <div class="metric">
            <div class="metric-value">{failed_downloads}</div>
            <div class="metric-label">Failed Downloads</div>
        </div>
        <div class="metric">
            <div class="metric-value">{unique_recipients}</div>
            <div class="metric-label">Unique Recipients</div>
        </div>
        <div style="margin-top: 20px;">
            <p><strong>⏱️ Time Metrics:</strong></p>
            <p>• Total Execution Time: <strong>{total_time_minutes:.2f} minutes</strong></p>
            <p>• Average Time per Report: <strong>{avg_time_per_report:.2f} seconds</strong></p>
            <p>• Estimated Manual Time: <strong>{manual_time_minutes} minutes</strong></p>
            <div class="highlight">
                <strong>⏰ Time Saved: {time_saved_minutes:.2f} minutes ({(time_saved_minutes/60):.1f} hours)</strong>
            </div>
        </div>
    </div>
    <div style="background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin-top: 20px;">
        <h4>📁 File Locations & Tracking</h4>
        <p><strong>Downloaded Files:</strong> reports/ directory</p>
        <p><strong>CSV Tracking File:</strong> downloaded_reports.csv (attached to this email)</p>
        <p><strong>Log File:</strong> Attached to this email</p>
        <p><strong>Total Files Downloaded:</strong> {len(downloaded_files)} files</p>
        <div style="background-color: #e8f4fd; padding: 10px; border-radius: 5px; margin-top: 10px; border-left: 3px solid #1976d2;">
            <p><strong>📋 Detailed Report Status:</strong> Please see the attached CSV file (downloaded_reports.csv) for complete details of each report download including dates, recipients, amounts, status, and file paths.</p>
        </div>
    </div>
    <div style="margin-top: 20px; padding: 15px; background-color: #e8f5e8; border-radius: 5px;">
        <h4>🔄 Process Flow Completed</h4>
        <p>✅ Logged into EZCater Merchant Portal</p>
        <p>✅ Navigated to Financials section</p>
        <p>✅ Filtered for last month's reports</p>
        <p>✅ Downloaded available reports sequentially</p>
        <p>✅ Tracked downloads in CSV file</p>
        <p>✅ Generated automation summary</p>
    </div>
    <p style="margin-top: 20px; font-style: italic; color: #666;">
        This automated process replaces manual navigation through EZCater's merchant portal,
        clicking through multiple pages, and downloading reports one by one.
        Time saved can be used for more strategic business activities.
    </p>
    <p><strong>Thank you for using EZCater Report Downloader!</strong></p>
    </body>
    </html>
    """
    try:
        # Prepare attachment list with log file and CSV file
        csv_file_path = os.path.join(os.path.dirname(__file__), "downloaded_reports.csv")
        attachment_paths = [LOG_FILE]
        if os.path.exists(csv_file_path):
            attachment_paths.append(csv_file_path)
            logger.info(f"Adding CSV file to email attachments: {csv_file_path}")
        else:
            logger.warning(f"CSV file not found for attachment: {csv_file_path}")

        send_email(
            from_address=email_config["from_address"],
            password=email_config["password"],
            to_addresses=email_config["to_addresses"],
            cc_addresses=email_config["cc_addresses"],
            subject=f"EZCater Report Downloader: {status} - {successful_downloads}/{total_reports_processed} Reports Downloaded",
            html_content=html_content,
            smtp_server=email_config["smtp_server"],
            smtp_port=email_config["smtp_port"],
            lsAttachmentPath=attachment_paths
        )
        logger.info(f"Notification email sent successfully: {status}")
    except Exception as e:
        logger.error(f"Failed to send notification email: {str(e)}")

def main():
    start_time = time.time()
    args = parse_args()
    logger.info(f"Starting EZManage Login automation with args: {vars(args)}")
    username = os.getlogin()
    logger.info(f"Automation initiated by user: {username}")
    exe_dir = get_exe_directory()
    try:
        selectors = load_selectors(os.path.join(exe_dir, "resources", "selectors_ezcater.json"))
        email_config_path = os.path.join(exe_dir, "resources", "email_config.json")
        email_config = load_email_config(email_config_path)
    except Exception as e:
        logger.error(f"Failed to load configuration: {str(e)}")
        if 'navigator' in locals():
            navigator.show_error_popup("Failed to load configuration files")
        sys.exit(1)
    site_url = args.site_url
    username_arg = args.username
    password = args.password
    driver_dir = args.driver_dir or os.path.join(exe_dir, "playwrightDrivers")
    headless = args.headless
    browser_args = args.browser_args or ["--start-maximized"]
    browser_type = args.browser_type
    state_file = os.path.join(exe_dir, f"state_{browser_type}.json")
    playwright_helper = None
    try:
        logger.info(f"Attempting automation with {browser_type}...")
        setup_playwright_driver(driver_path=driver_dir, browser_type=browser_type)
        storage_state = state_file if os.path.exists(state_file) else None
        playwright_helper = PlaywrightHelper(bHeadless=headless, storage_state=storage_state, browser_type=browser_type)
        logger.info(f"Launching {browser_type} browser...")
        playwright_helper.start_browser(browser_args=browser_args)
        navigator = CWebNavigator(playwright_helper)
        success = False
        try:
            if storage_state:
                success = handle_session_validation(
                    navigator=navigator,
                    playwright_helper=playwright_helper,
                    site_url=site_url,
                    username=username_arg,
                    password=password,
                    state_file=state_file,
                    selectors=selectors
                )
            else:
                success = handle_full_login(
                    navigator=navigator,
                    playwright_helper=playwright_helper,
                    site_url=site_url,
                    username=username_arg,
                    password=password,
                    state_file=state_file,
                    selectors=selectors
                )
            if success:
                post_login_success = navigator.post_login_actions(selectors, args.from_date, args.to_date)
                if not post_login_success:
                    logger.error(f"Failed to complete post-login actions ({browser_type}). Exiting...")
                    navigator.show_error_popup(f"Failed to complete post-login actions with {browser_type}. Please check logs and rerun.")
                    sys.exit(1)
            total_time = time.time() - start_time
            if success and post_login_success:
                logger.info(f"Automation completed successfully with {browser_type}. Final URL: {navigator.redirected_url}")
                send_notification_email(True, navigator.downloaded_files, navigator.download_stats, total_time, browser_type, email_config, username)
            else:
                logger.error(f"Automation failed with {browser_type}. Exiting...")
                navigator.show_error_popup(f"Automation failed with {browser_type}. Please check logs and rerun.")
                send_notification_email(False, navigator.downloaded_files, navigator.download_stats, total_time, browser_type, email_config, username)
                sys.exit(1)
        except Exception as e:
            logger.error(f"Error in automation with {browser_type}: {str(e)}", exc_info=True)
            navigator.show_error_popup(f"Error in automation with {browser_type}. Please check logs and rerun.")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Error in main execution: {str(e)}", exc_info=True)
        if 'navigator' in locals():
            navigator.show_error_popup("Error in automation setup. Please check logs and rerun.")
        sys.exit(1)
    finally:
        if playwright_helper:
            playwright_helper.close()

if __name__ == "__main__":
    main()