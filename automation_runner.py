import argparse
import os
import sys
import logging
import json
from pathlib import Path
from enum import Enum
from datetime import datetime
from logging import get<PERSON><PERSON><PERSON>, FileHandler, Formatter, INFO
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import smtplib

class VendorType(Enum):
    """Supported vendor types for automation."""
    ADYEN = "adyen"
    EZCATER = "ezcater"

# Shared utility functions (extracted from main.py)
def setup_logger(log_dir="logs"):
    """Configure logging to file and console."""
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f"log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    logger = getLogger('AutomationRunner')
    logger.setLevel(INFO)
    
    file_handler = FileHandler(log_file)
    file_handler.setLevel(INFO)
    file_handler.setFormatter(Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(INFO)
    console_handler.setFormatter(Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger, log_file

def load_selectors(json_path):
    """Load selectors from a JSON file."""
    try:
        with open(json_path, 'r') as f:
            selectors = json.load(f)
        print(f"Loaded selectors from {json_path}")
        return selectors
    except FileNotFoundError:
        print(f"Selectors JSON file not found: {json_path}")
        raise FileNotFoundError(f"Selectors JSON file not found: {json_path}")
    except json.JSONDecodeError as e:
        print(f"Invalid JSON format in {json_path}: {str(e)}")
        raise ValueError(f"Invalid JSON format in {json_path}: {str(e)}")
    except Exception as e:
        print(f"Error loading selectors from {json_path}: {str(e)}")
        raise

def load_email_config(json_path):
    """Load email configuration from a JSON file."""
    try:
        with open(json_path, 'r') as f:
            config = json.load(f)
        required_fields = ["from_address", "password", "to_addresses", "cc_addresses", "smtp_server", "smtp_port"]
        missing_fields = [field for field in required_fields if field not in config]
        if missing_fields:
            raise ValueError(f"Missing required fields in email_config.json: {', '.join(missing_fields)}")
        print(f"Loaded email configuration from {json_path}")
        return config
    except FileNotFoundError:
        print(f"Email config JSON file not found: {json_path}")
        raise FileNotFoundError(f"Email config JSON file not found: {json_path}")
    except json.JSONDecodeError as e:
        print(f"Invalid JSON format in {json_path}: {str(e)}")
        raise ValueError(f"Invalid JSON format in {json_path}: {str(e)}")
    except Exception as e:
        print(f"Error loading email config from {json_path}: {str(e)}")
        raise

def send_email(from_address, password, to_addresses, cc_addresses, subject, html_content, smtp_server, smtp_port, lsAttachmentPath=[]):
    """
    Sends an email with the given parameters.
    """
    message = MIMEMultipart("alternative")
    message["From"] = from_address
    message["To"] = ", ".join(to_addresses)
    message["Cc"] = ", ".join(cc_addresses)
    message["Subject"] = subject

    message.attach(MIMEText(html_content, "html"))

    if lsAttachmentPath:
        try:
            for attachment_path in lsAttachmentPath:
                if os.path.isfile(attachment_path):
                    with open(attachment_path, "rb") as attachment:
                        part = MIMEBase('application', 'octet-stream')
                        part.set_payload(attachment.read())
                        encoders.encode_base64(part)
                        part.add_header('Content-Disposition', f'attachment; filename={os.path.basename(attachment_path)}')
                        message.attach(part)
                    print(f"Attached file: {attachment_path}")
                else:
                    print(f"Attachment file does not exist: {attachment_path}")
        except Exception as e:
            print(f"Failed to add attachments in the email: {e}")

    all_recipients = to_addresses + cc_addresses

    try:
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(from_address, password)
            server.sendmail(from_address, all_recipients, message.as_string())
        print("Email sent successfully!")
    except Exception as e:
        print(f"Error sending email: {e}")

def get_exe_directory():
    """Return the directory of the executable or script."""
    if getattr(sys, 'frozen', False):
        return os.path.dirname(sys.executable)
    return os.path.dirname(os.path.abspath(__file__))

def parse_args():
    """Unified argument parser for all vendors."""
    parser = argparse.ArgumentParser(description='Multi-Vendor Report Automation Runner')
    
    # Required vendor selection
    parser.add_argument('--vendor', type=str, required=True, 
                       choices=[v.value for v in VendorType],
                       help='Vendor to run automation for (adyen or ezcater)')
    
    # Common arguments
    parser.add_argument('--headless', type=bool, default=False, help='Run in headless mode')
    parser.add_argument('--browser-type', type=str, default='chromium', 
                       choices=['chromium', 'firefox'], 
                       help='Browser type to use (chromium or firefox, default: chromium)')
    parser.add_argument('--driver-dir', type=str, help='Directory for Playwright drivers')
    parser.add_argument('--browser-args', type=str, nargs='*', help='Custom browser arguments')
    parser.add_argument('--state-file', type=str, default='state.json', help='Path to storage state file')
    
    # Adyen-specific arguments
    parser.add_argument('--xlsx-file', type=str, default='stores.xlsx', 
                       help='Path to XLSX file with account data (required for Adyen)')
    parser.add_argument('--adyen-site-url', type=str, 
                       help='Adyen login page URL')
    parser.add_argument('--adyen-username', type=str, 
                       help='Adyen username for login')
    parser.add_argument('--adyen-password', type=str, 
                       help='Adyen password for login')
    parser.add_argument('--adyen-from-date', type=str, 
                       help='Adyen start date for report (YYYY-MM-DD), defaults to previous month first day')
    parser.add_argument('--adyen-to-date', type=str, 
                       help='Adyen end date for report (YYYY-MM-DD), defaults to previous month last day')
    parser.add_argument('--email-config', type=str, 
                       help='Path to email config JSON file')
    parser.add_argument('--download-dir', type=str, 
                       help='Base directory for downloads (date-based subfolder will be created)')
    
    # EZCater-specific arguments  
    parser.add_argument('--ezcater-site-url', type=str, default='https://ezmanage.ezcater.com/', 
                       help='EZCater login page URL')
    parser.add_argument('--ezcater-username', type=str, default='<EMAIL>', 
                       help='EZCater username for login')
    parser.add_argument('--ezcater-password', type=str, default='Freshfoods@2023', 
                       help='EZCater password for login')
    parser.add_argument('--ezcater-from-date', type=str, 
                       help='EZCater start date for report filtering (mm/dd/yy)')
    parser.add_argument('--ezcater-to-date', type=str, 
                       help='EZCater end date for report filtering (mm/dd/yy)')
    
    return parser.parse_args()

def validate_vendor_args(args):
    """Validate vendor-specific required arguments."""
    if args.vendor == VendorType.ADYEN.value:
        # Adyen validation - xlsx-file is required
        if not args.xlsx_file or not os.path.exists(args.xlsx_file):
            print(f"Error: --xlsx-file is required for Adyen automation and must exist. Provided: {args.xlsx_file}")
            sys.exit(1)
            
    elif args.vendor == VendorType.EZCATER.value:
        # EZCater validation - basic validation (defaults are set)
        if not args.ezcater_site_url or not args.ezcater_username or not args.ezcater_password:
            print("Error: EZCater requires site-url, username, and password")
            sys.exit(1)

def map_args_for_vendor(args):
    """Map unified args to vendor-specific argument structure."""
    if args.vendor == VendorType.ADYEN.value:
        # Map to Adyen's expected argument structure
        mapped_args = argparse.Namespace()
        mapped_args.site_url = args.adyen_site_url
        mapped_args.username = args.adyen_username
        mapped_args.password = args.adyen_password
        mapped_args.driver_dir = args.driver_dir
        mapped_args.headless = args.headless
        mapped_args.browser_args = args.browser_args
        mapped_args.browser_type = args.browser_type
        mapped_args.state_file = args.state_file
        mapped_args.xlsx_file = args.xlsx_file
        mapped_args.from_date = args.adyen_from_date
        mapped_args.to_date = args.adyen_to_date
        mapped_args.email_config = args.email_config
        mapped_args.download_dir = args.download_dir
        return mapped_args
        
    elif args.vendor == VendorType.EZCATER.value:
        # Map to EZCater's expected argument structure
        mapped_args = argparse.Namespace()
        mapped_args.site_url = args.ezcater_site_url
        mapped_args.username = args.ezcater_username
        mapped_args.password = args.ezcater_password
        mapped_args.driver_dir = args.driver_dir
        mapped_args.headless = args.headless
        mapped_args.browser_args = args.browser_args
        mapped_args.browser_type = args.browser_type
        mapped_args.state_file = args.state_file
        mapped_args.from_date = args.ezcater_from_date
        mapped_args.to_date = args.ezcater_to_date
        return mapped_args

def main():
    """Main entry point for the automation runner."""
    print("🚀 Multi-Vendor Report Automation Runner")
    print("=" * 50)
    
    args = parse_args()
    
    print(f"Selected Vendor: {args.vendor.upper()}")
    print(f"Browser Type: {args.browser_type}")
    print(f"Headless Mode: {args.headless}")
    
    # Validate vendor-specific arguments
    validate_vendor_args(args)
    
    # Map arguments to vendor-specific structure
    vendor_args = map_args_for_vendor(args)
    
    try:
        if args.vendor == VendorType.ADYEN.value:
            print("🔄 Starting Adyen automation...")
            import adyen_automation
            adyen_automation.run_adyen_automation(vendor_args)
            
        elif args.vendor == VendorType.EZCATER.value:
            print("🔄 Starting EZCater automation...")
            import ezcater_automation
            ezcater_automation.run_ezcater_automation(vendor_args)
            
        print("✅ Automation completed successfully!")
        
    except ImportError as e:
        print(f"❌ Error: Could not import vendor automation module: {e}")
        print("Make sure the vendor automation files are in the same directory.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error during automation: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
