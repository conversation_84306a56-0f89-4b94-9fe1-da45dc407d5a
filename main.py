import argparse
import os
import sys
import logging
import pandas as pd
import json
from pathlib import Path
from playwright.sync_api import sync_playwright, TimeoutError
import re
from datetime import datetime, timedelta, timezone
from logging import get<PERSON>ogger, FileHandler, Formatter, INFO
import tkinter as tk
from tkinter import messagebox, ttk
import urllib.parse
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEB<PERSON>
from email import encoders
import smtplib
import time

# Setup logging
def setup_logger(log_dir="logs"):
    """Configure logging to file and console."""
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f"log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    logger = getLogger('AdyenDownloader')
    logger.setLevel(INFO)
    
    file_handler = FileHandler(log_file)
    file_handler.setLevel(INFO)
    file_handler.setFormatter(Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(INFO)
    console_handler.setFormatter(Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger, log_file

logger, LOG_FILE = setup_logger()

def load_selectors(json_path="selectors.json"):
    """Load selectors from a JSON file."""
    try:
        with open(json_path, 'r') as f:
            selectors = json.load(f)
        logger.info(f"Loaded selectors from {json_path}")
        return selectors
    except FileNotFoundError:
        logger.error(f"Selectors JSON file not found: {json_path}")
        raise FileNotFoundError(f"Selectors JSON file not found: {json_path}")
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON format in {json_path}: {str(e)}")
        raise ValueError(f"Invalid JSON format in {json_path}: {str(e)}")
    except Exception as e:
        logger.error(f"Error loading selectors from {json_path}: {str(e)}")
        raise

def load_email_config(json_path):
    """Load email configuration from a JSON file."""
    try:
        with open(json_path, 'r') as f:
            config = json.load(f)
        required_fields = ["from_address", "password", "to_addresses", "cc_addresses", "smtp_server", "smtp_port"]
        missing_fields = [field for field in required_fields if field not in config]
        if missing_fields:
            raise ValueError(f"Missing required fields in email_config.json: {', '.join(missing_fields)}")
        logger.info(f"Loaded email configuration from {json_path}")
        return config
    except FileNotFoundError:
        logger.error(f"Email config JSON file not found: {json_path}")
        raise FileNotFoundError(f"Email config JSON file not found: {json_path}")
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON format in {json_path}: {str(e)}")
        raise ValueError(f"Invalid JSON format in {json_path}: {str(e)}")
    except Exception as e:
        logger.error(f"Error loading email config from {json_path}: {str(e)}")
        raise

@staticmethod
def send_email(from_address, password, to_addresses, cc_addresses, subject, html_content, smtp_server, smtp_port, lsAttachmentPath=[]):
    """
    Sends an email with the given parameters.
    """
    message = MIMEMultipart("alternative")
    message["From"] = from_address
    message["To"] = ", ".join(to_addresses)
    message["Cc"] = ", ".join(cc_addresses)
    message["Subject"] = subject

    message.attach(MIMEText(html_content, "html"))

    if lsAttachmentPath:
        try:
            for attachment_path in lsAttachmentPath:
                if os.path.isfile(attachment_path):
                    with open(attachment_path, "rb") as attachment:
                        part = MIMEBase('application', 'octet-stream')
                        part.set_payload(attachment.read())
                        encoders.encode_base64(part)
                        part.add_header('Content-Disposition', f'attachment; filename={os.path.basename(attachment_path)}')
                        message.attach(part)
                    logger.info(f"Attached file: {attachment_path}")
                else:
                    logger.error(f"Attachment file does not exist: {attachment_path}")
        except Exception as e:
            logger.error(f"Failed to add attachments in the email: {e}")

    all_recipients = to_addresses + cc_addresses

    try:
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(from_address, password)
            server.sendmail(from_address, all_recipients, message.as_string())
        logger.info("Email sent successfully!")
    except Exception as e:
        logger.error(f"Error sending email: {e}")

class PlaywrightHelper:
    """Handles browser setup and common actions."""
    def __init__(self, bHeadless=False, storage_state=None, browser_type="chromium"):
        """Initialize Playwright browser with optional storage state and browser type."""
        self.playwright = None
        self.browser = None
        self.context = None
        self.objPage = None
        self.headless = bHeadless
        self.storage_state = storage_state
        self.browser_type = browser_type.lower()

    def start_browser(self, browser_args=None):
        """Launch specified browser (Chromium or Firefox) with arguments."""
        try:
            self.playwright = sync_playwright().start()
            browser_args = browser_args or ["--start-maximized"]
            if self.browser_type == "firefox":
                self.browser = self.playwright.firefox.launch(headless=self.headless, args=browser_args)
                logger.info("Browser launched successfully (Firefox).")
            else:
                self.browser = self.playwright.chromium.launch(headless=self.headless, args=browser_args)
                logger.info("Browser launched successfully (Chromium).")
            self.context = self.browser.new_context(
                no_viewport=True,
                storage_state=self.storage_state if self.storage_state else None
            )
            self.objPage = self.context.new_page()
            self.objPage.on("pageerror", lambda error: logger.error(f"Page error ({self.browser_type}): {str(error)}"))
        except Exception as e:
            logger.error(f"Failed to launch browser ({self.browser_type}): {str(e)}", exc_info=True)
            raise

    def login(self, url, username, password, selectors):
        """Perform initial login steps (username and password)."""
        try:
            self.objPage.goto(url)
            logger.info(f"Navigated to login page: {url} ({self.browser_type})")
            self.objPage.locator(selectors["username_field"]).fill(username)
            self.objPage.locator(selectors["username_next_button"]).click()
            logger.info(f"Username entered and Next button clicked ({self.browser_type}).")
            self.objPage.wait_for_load_state("networkidle", timeout=35000 if self.browser_type == "firefox" else 30000)
            self.objPage.locator(selectors["password_field"]).fill(password)
            self.objPage.locator(selectors["password_next_button"]).click()
            logger.info(f"Password entered and Next button clicked ({self.browser_type}).")
            self.objPage.wait_for_load_state("networkidle", timeout=35000 if self.browser_type == "firefox" else 30000)
        except TimeoutError:
            logger.error(f"Timeout during login process ({self.browser_type}).", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"Error during login ({self.browser_type}): {str(e)}", exc_info=True)
            raise

    def save_storage_state(self, path):
        """Save the browser's storage state to a file."""
        try:
            self.context.storage_state(path=path)
            logger.info(f"Storage state saved to {path} ({self.browser_type})")
        except Exception as e:
            logger.error(f"Failed to save storage state ({self.browser_type}): {str(e)}", exc_info=True)
            raise

    def close(self):
        """Close browser and Playwright instance."""
        try:
            if self.context:
                self.context.close()
            if self.browser:
                self.browser.close()
            if self.playwright:
                self.playwright.stop()
            logger.info(f"Browser closed successfully ({self.browser_type}).")
        except Exception as e:
            logger.error(f"Error closing browser ({self.browser_type}): {str(e)}", exc_info=True)

class CWebNavigator:
    """Handles the login workflow, including OTP verification and post-login actions."""
    def __init__(self, playwright_instance):
        """Initialize with PlaywrightHelper instance."""
        self.playwright = playwright_instance
        self.objPage = self.playwright.objPage
        self.browser_type = self.playwright.browser_type
        self.downloaded_files = []  # Track downloaded files
        self.download_stats = []  # Track stats: store, status, from_date, to_date, file_path, time_taken

    def showOTPPopUp(self):
        """Display a Tkinter pop-up to remind the user to enter OTP."""
        try:
            root = tk.Tk()
            root.withdraw()
            root.attributes('-topmost', True)
            logger.info(f"Displaying Tkinter OTP reminder pop-up ({self.browser_type}).")
            messagebox.showinfo(
                title="Adyen Downloader - OTP Required",
                message="Please enter the 6-digit OTP sent to your registered device to continue.",
                parent=root
            )
            logger.info(f"Tkinter OTP reminder pop-up Continue button clicked ({self.browser_type}).")
            root.destroy()
        except Exception as e:
            logger.error(f"Error in Tkinter OTP pop-up ({self.browser_type}): {str(e)}", exc_info=True)
            raise

    def showStartProcessPopUp(self):
        """Display a Tkinter pop-up to notify the user that the report generation process is starting."""
        try:
            root = tk.Tk()
            root.withdraw()
            root.attributes('-topmost', True)
            logger.info(f"Displaying Tkinter start process pop-up ({self.browser_type}).")
            messagebox.showinfo(
                title="Adyen Downloader - Process Starting",
                message="Starting the report generation process for your accounts.",
                parent=root
            )
            logger.info(f"Tkinter start process pop-up OK button clicked ({self.browser_type}).")
            root.destroy()
        except Exception as e:
            logger.error(f"Error in Tkinter start process pop-up ({self.browser_type}): {str(e)}", exc_info=True)
            raise

    def showEndProcessPopUp(self, stats, total_time, time_saved):
        """Display a Tkinter pop-up with detailed automation statistics in a table."""
        try:
            root = tk.Tk()
            root.title("Adyen Downloader - Automation Statistics")
            root.attributes('-topmost', True)
            root.geometry("900x600")

            # Create main frame
            main_frame = ttk.Frame(root, padding="10")
            main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

            # Summary label
            ttk.Label(
                main_frame,
                text=f"Automation Completed\nTotal Time: {total_time:.2f} seconds | Time Saved: {time_saved:.2f} minutes",
                font=("Arial", 12, "bold")
            ).grid(row=0, column=0, columnspan=2, pady=10)

            # Treeview for statistics
            columns = ("Store", "Status", "From Date", "To Date", "File Location", "Time Taken (s)")
            tree = ttk.Treeview(main_frame, columns=columns, show="headings")
            tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

            # Set column headings
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=150, anchor=tk.W)

            # Insert data
            for stat in stats:
                tree.insert("", tk.END, values=(
                    stat["store"],
                    stat["status"],
                    stat["from_date"],
                    stat["to_date"],
                    stat["file_path"] if stat["file_path"] else "N/A",
                    f"{stat['time_taken']:.2f}" if stat["time_taken"] else "N/A"
                ))

            # Scrollbar
            scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=tree.yview)
            scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
            tree.configure(yscrollcommand=scrollbar.set)

            # OK button
            ttk.Button(main_frame, text="OK", command=root.destroy).grid(row=2, column=0, columnspan=2, pady=10)

            logger.info(f"Displaying Tkinter statistics pop-up ({self.browser_type}).")
            root.mainloop()
            logger.info(f"Tkinter statistics pop-up closed ({self.browser_type}).")
        except Exception as e:
            logger.error(f"Error in Tkinter statistics pop-up ({self.browser_type}): {str(e)}", exc_info=True)
            raise

    def showErrorPopUp(self, error_message="An error occurred during the process."):
        """Display a Tkinter pop-up to notify the user of an error."""
        try:
            root = tk.Tk()
            root.withdraw()
            root.attributes('-topmost', True)
            logger.info(f"Displaying Tkinter error pop-up ({self.browser_type}).")
            messagebox.showerror(
                title="Adyen Downloader - Error",
                message=f"{error_message} Please check the logs for details or contact support.",
                parent=root
            )
            logger.info(f"Tkinter error pop-up OK button clicked ({self.browser_type}).")
            root.destroy()
        except Exception as e:
            logger.error(f"Error in Tkinter error pop-up ({self.browser_type}): {str(e)}", exc_info=True)
            raise

    def validate_dashboard(self, selectors):
        """Validate that the dashboard is loaded and ready for interaction."""
        max_attempts = 3
        attempt = 0
        timeout = 70000 if self.browser_type == "firefox" else 60000
        while attempt < max_attempts:
            try:
                logger.info(f"Post-pop-up dashboard validation attempt {attempt + 1}. URL: {self.objPage.url} ({self.browser_type})")
                dashboard_element = self.objPage.locator(selectors["dropdown"])
                count = dashboard_element.count()
                logger.info(f"Dropdown selector count: {count}, Selector: {selectors['dropdown']} ({self.browser_type})")
                if count > 0:
                    dashboard_element.wait_for(state="visible", timeout=timeout)
                    logger.info(f"Dashboard validated successfully ({self.browser_type}).")
                    return True
                if "essentials-live.adyen.com" in self.objPage.url.lower():
                    logger.info(f"Dashboard URL confirmed: {self.objPage.url} ({self.browser_type})")
                    return True
                logger.warning(f"Dashboard element not yet visible ({self.browser_type}).")
                self.objPage.wait_for_timeout(2000)
                attempt += 1
            except Exception as e:
                logger.warning(f"Dashboard validation attempt {attempt + 1} failed: {str(e)} ({self.browser_type})")
                if "essentials-live.adyen.com" in self.objPage.url.lower():
                    logger.info(f"Dashboard URL confirmed: {self.objPage.url} ({self.browser_type})")
                    return True
                logger.warning(f"Retrying dashboard validation ({self.browser_type}).")
                self.objPage.wait_for_timeout(2000)
                attempt += 1
        logger.error(f"Failed to validate dashboard after retries ({self.browser_type}).")
        return False

    def handleOTPProcess(self, selectors):
        """Handle OTP pop-up and input verification."""
        try:
            self.showOTPPopUp()
            logger.info(f"Checking for OTP notification pop-up ({self.browser_type})...")
            continue_button = self.objPage.locator(selectors["otp_continue_button"])
            if continue_button.count() > 0:
                continue_button.click()
                logger.info(f"OTP notification pop-up continue button clicked ({self.browser_type}).")
            else:
                logger.warning(f"OTP notification pop-up not found; proceeding to OTP input ({self.browser_type}).")
            self.objPage.wait_for_load_state("networkidle", timeout=35000 if self.browser_type == "firefox" else 30000)

            otp_input = self.objPage.locator(selectors["otp_input_field"])
            otp_input.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
            logger.info(f"OTP input field is visible ({self.browser_type}).")

            logger.info(f"Waiting for user to enter 6-digit OTP ({self.browser_type})...")
            max_attempts = 30
            attempt = 0
            while attempt < max_attempts:
                otp_value = otp_input.input_value()
                if self.verify_otp(otp_value):
                    logger.info(f"Valid 6-digit OTP detected ({self.browser_type}).")
                    break
                self.objPage.wait_for_timeout(1000)
                attempt += 1

            if attempt >= max_attempts:
                logger.error(f"Timeout: User did not enter a valid 6-digit OTP ({self.browser_type}).")
                return False

            self.objPage.locator(selectors["otp_submit_button"]).click()
            logger.info(f"OTP submitted successfully ({self.browser_type}).")
            self.objPage.wait_for_load_state("networkidle", timeout=35000 if self.browser_type == "firefox" else 30000)

            max_attempts = 60
            attempt = 0
            while attempt < max_attempts:
                try:
                    logger.info(f"Dashboard check attempt {attempt + 1}. URL: {self.objPage.url} ({self.browser_type})")
                    dashboard_element = self.objPage.locator(selectors["dropdown"])
                    count = dashboard_element.count()
                    logger.info(f"Dropdown selector count: {count}, Selector: {selectors['dropdown']} ({self.browser_type})")
                    if count > 0:
                        dashboard_element.wait_for(state="visible", timeout=1000)
                        logger.info(f"Dashboard loaded successfully ({self.browser_type}).")
                        return True
                    if "essentials-live.adyen.com" in self.objPage.url.lower():
                        logger.info(f"Dashboard URL confirmed: {self.objPage.url} ({self.browser_type})")
                        return True
                    logger.warning(f"Dashboard element not yet visible ({self.browser_type}).")
                    self.objPage.wait_for_timeout(2000)
                    attempt += 1
                except Exception as e:
                    logger.warning(f"Dashboard check attempt {attempt + 1} failed: {str(e)} ({self.browser_type})")
                    if "essentials-live.adyen.com" in self.objPage.url.lower():
                        logger.info(f"Dashboard URL confirmed: {self.objPage.url} ({self.browser_type})")
                        return True
                    logger.warning(f"Retrying dashboard check ({self.browser_type}).")
                    self.objPage.wait_for_timeout(2000)
                    attempt += 1

            if "authn-live.adyen.com" in self.objPage.url.lower():
                logger.error(f"Redirected to login page after OTP submission ({self.browser_type}).")
                return False
            logger.error(f"Timeout: Failed to load dashboard after OTP submission ({self.browser_type}).")
            return False

        except TimeoutError:
            logger.error(f"Timeout during OTP process ({self.browser_type}).", exc_info=True)
            return False
        except Exception as e:
            logger.error(f"Error during OTP process ({self.browser_type}): {str(e)}", exc_info=True)
            return False

    def verify_otp(self, otp):
        """Verify that OTP is a 6-digit number."""
        pattern = r'^\d{6}$'
        return bool(re.match(pattern, otp))

    def get_date_range(self, from_date=None, to_date=None):
        """Return date range, using user input or defaulting to previous month's dates."""
        try:
            if from_date and to_date:
                datetime.strptime(from_date, "%Y-%m-%d")
                datetime.strptime(to_date, "%Y-%m-%d")
                logger.info(f"Using user-provided dates: from {from_date} to {to_date} ({self.browser_type})")
                return {"from_date": from_date, "to_date": to_date}
            else:
                today = datetime.now()
                first_day_of_current_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                last_day_of_previous_month = first_day_of_current_month - timedelta(days=1)
                first_day_of_previous_month = last_day_of_previous_month.replace(day=1)
                logger.info(f"Using default previous month dates ({self.browser_type})")
                return {
                    "from_date": first_day_of_previous_month.strftime("%Y-%m-%d"),
                    "to_date": last_day_of_previous_month.strftime("%Y-%m-%d")
                }
        except ValueError as e:
            logger.error(f"Invalid date format provided: {str(e)} ({self.browser_type})")
            raise ValueError("Dates must be in YYYY-MM-DD format")

    def perform_search_from_xlsx(self, xlsx_file, download_dir, from_date=None, to_date=None, selectors=None):
        """Read XLSX and perform search and post-selection actions for each Account name."""
        try:
            if not os.path.exists(xlsx_file):
                logger.error(f"XLSX file not found: {xlsx_file} ({self.browser_type})")
                raise FileNotFoundError(f"XLSX file not found: {xlsx_file}")

            df = pd.read_excel(xlsx_file, engine='openpyxl')
            if 'Account name' not in df.columns:
                logger.error(f"XLSX file must contain an 'Account name' column ({self.browser_type}).")
                raise ValueError("XLSX file must contain an 'Account name' column.")

            failed_accounts = []
            dates = self.get_date_range(from_date, to_date)
            from_date_val = dates["from_date"]
            to_date_val = dates["to_date"]

            for index, row in df.iterrows():
                try:
                    account_name = str(row['Account name']).strip()
                    if not account_name:
                        logger.warning(f"Skipping empty Account name in row {row.get('Sr No', index + 1)} ({self.browser_type})")
                        self.download_stats.append({
                            "store": f"Row {row.get('Sr No', index + 1)}",
                            "status": "Skipped (Empty Account Name)",
                            "from_date": from_date_val,
                            "to_date": to_date_val,
                            "file_path": None,
                            "time_taken": None
                        })
                        continue

                    logger.info(f"Processing Account name: {account_name} ({self.browser_type})")
                    start_time = time.time()

                    max_retries = 3
                    retry = 0
                    while retry < max_retries:
                        try:
                            dropdown = self.objPage.locator(selectors["dropdown"])
                            logger.info(f"Attempting to locate dropdown. Selector: {selectors['dropdown']}, URL: {self.objPage.url} ({self.browser_type})")
                            dropdown.wait_for(state="visible", timeout=70000 if self.browser_type == "firefox" else 60000)
                            dropdown.click()
                            logger.info(f"Clicked first dropdown to open search box ({self.browser_type}).")
                            self.objPage.wait_for_load_state("domcontentloaded", timeout=10000)
                            break
                        except TimeoutError:
                            logger.error(f"Dropdown not found for {account_name} on attempt {retry + 1} ({self.browser_type}).")
                            retry += 1
                            if retry < max_retries:
                                logger.info(f"Retrying dropdown for {account_name} ({self.browser_type})...")
                                self.objPage.wait_for_timeout(2000)
                            continue
                    if retry >= max_retries:
                        logger.error(f"Failed to locate dropdown for {account_name} after {max_retries} attempts ({self.browser_type}).")
                        failed_accounts.append(account_name)
                        self.download_stats.append({
                            "store": account_name,
                            "status": "Failed (Dropdown Not Found)",
                            "from_date": from_date_val,
                            "to_date": to_date_val,
                            "file_path": None,
                            "time_taken": None
                        })
                        continue

                    try:
                        search_box = self.objPage.locator(selectors["search_box"])
                        search_box.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                        search_box.fill(account_name)
                        logger.info(f"Entered '{account_name}' into search box ({self.browser_type}).")
                        self.objPage.wait_for_load_state("domcontentloaded", timeout=10000)
                    except TimeoutError:
                        logger.error(f"Search box not found for {account_name} ({self.browser_type}).")
                        failed_accounts.append(account_name)
                        self.download_stats.append({
                            "store": account_name,
                            "status": "Failed (Search Box Not Found)",
                            "from_date": from_date_val,
                            "to_date": to_date_val,
                            "file_path": None,
                            "time_taken": None
                        })
                        continue

                    result_selector = f"text='{account_name}'"
                    result = self.objPage.locator(result_selector)
                    retry = 0
                    while retry < max_retries:
                        try:
                            result.wait_for(state="visible", timeout=10000)
                            if result.count() > 0:
                                result.click()
                                logger.info(f"Selected search result: {account_name} ({self.browser_type})")
                                self.objPage.wait_for_load_state("networkidle", timeout=35000 if self.browser_type == "firefox" else 30000)
                                break
                            else:
                                logger.warning(f"No search result found for: {account_name}. Attempting to proceed ({self.browser_type}).")
                                break
                        except TimeoutError:
                            logger.warning(f"Timeout waiting for search result: {account_name} on attempt {retry + 1} ({self.browser_type}).")
                            retry += 1
                            if retry < max_retries:
                                logger.info(f"Retrying search result for {account_name} ({self.browser_type})...")
                                self.objPage.wait_for_timeout(2000)
                            continue
                    if retry >= max_retries:
                        logger.error(f"Failed to select search result for {account_name} after {max_retries} attempts ({self.browser_type}).")
                        failed_accounts.append(account_name)
                        self.download_stats.append({
                            "store": account_name,
                            "status": "Failed (Search Result Not Found)",
                            "from_date": from_date_val,
                            "to_date": to_date_val,
                            "file_path": None,
                            "time_taken": None
                        })
                        continue

                    retry = 0
                    while retry < max_retries:
                        try:
                            second_dropdown = self.objPage.locator(selectors["second_dropdown"])
                            second_dropdown.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                            second_dropdown.click()
                            logger.info(f"Clicked second dropdown ({self.browser_type}).")
                            self.objPage.wait_for_load_state("domcontentloaded", timeout=10000)
                            break
                        except TimeoutError:
                            logger.error(f"Second dropdown not found for {account_name} on attempt {retry + 1} ({self.browser_type}).")
                            retry += 1
                            if retry < max_retries:
                                logger.info(f"Retrying second dropdown for {account_name} ({self.browser_type})...")
                                self.objPage.wait_for_timeout(2000)
                            continue
                    if retry >= max_retries:
                        logger.error(f"Failed to locate second dropdown for {account_name} after {max_retries} attempts ({self.browser_type}).")
                        failed_accounts.append(account_name)
                        self.download_stats.append({
                            "store": account_name,
                            "status": "Failed (Second Dropdown Not Found)",
                            "from_date": from_date_val,
                            "to_date": to_date_val,
                            "file_path": None,
                            "time_taken": None
                        })
                        continue

                    retry = 0
                    while retry < max_retries:
                        try:
                            second_dropdown_option = self.objPage.locator(selectors["second_dropdown_option"])
                            second_dropdown_option.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                            if second_dropdown_option.count() > 0:
                                second_dropdown_option.click()
                                logger.info(f"Selected option in second dropdown ({self.browser_type}).")
                                self.objPage.wait_for_load_state("networkidle", timeout=35000 if self.browser_type == "firefox" else 30000)
                                break
                            else:
                                logger.error(f"Second dropdown option not found for {account_name} ({self.browser_type}).")
                                failed_accounts.append(account_name)
                                self.download_stats.append({
                                    "store": account_name,
                                    "status": "Failed (Second Dropdown Option Not Found)",
                                    "from_date": from_date_val,
                                    "to_date": to_date_val,
                                    "file_path": None,
                                    "time_taken": None
                                })
                                break
                        except TimeoutError:
                            logger.error(f"Second dropdown option not found for {account_name} on attempt {retry + 1} ({self.browser_type}).")
                            retry += 1
                            if retry < max_retries:
                                logger.info(f"Retrying second dropdown option for {account_name} ({self.browser_type})...")
                                self.objPage.wait_for_timeout(2000)
                            continue
                    if retry >= max_retries:
                        logger.error(f"Failed to select second dropdown option for {account_name} after {max_retries} attempts ({self.browser_type}).")
                        failed_accounts.append(account_name)
                        self.download_stats.append({
                            "store": account_name,
                            "status": "Failed (Second Dropdown Option Not Found)",
                            "from_date": from_date_val,
                            "to_date": to_date_val,
                            "file_path": None,
                            "time_taken": None
                        })
                        continue

                    retry = 0
                    while retry < max_retries:
                        try:
                            manage_report_button = self.objPage.locator(selectors["manage_report_button"])
                            manage_report_button.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                            if manage_report_button.count() > 0:
                                manage_report_button.click()
                                logger.info(f"Clicked 'Manage Report' button ({self.browser_type}).")
                                break
                            else:
                                logger.error(f"'Manage Report' button not found for {account_name} ({self.browser_type}).")
                                failed_accounts.append(account_name)
                                self.download_stats.append({
                                    "store": account_name,
                                    "status": "Failed (Manage Report Button Not Found)",
                                    "from_date": from_date_val,
                                    "to_date": to_date_val,
                                    "file_path": None,
                                    "time_taken": None
                                })
                                break
                        except TimeoutError:
                            logger.error(f"'Manage Report' button not found for {account_name} on attempt {retry + 1} ({self.browser_type}).")
                            retry += 1
                            if retry < max_retries:
                                logger.info(f"Retrying manage report button for {account_name} ({self.browser_type})...")
                                self.objPage.wait_for_timeout(2000)
                            continue
                    if retry >= max_retries:
                        logger.error(f"Failed to click 'Manage Report' button for {account_name} after {max_retries} attempts ({self.browser_type}).")
                        failed_accounts.append(account_name)
                        self.download_stats.append({
                            "store": account_name,
                            "status": "Failed (Manage Report Button Not Found)",
                            "from_date": from_date_val,
                            "to_date": to_date_val,
                            "file_path": None,
                            "time_taken": None
                        })
                        continue

                    retry = 0
                    while retry < max_retries:
                        try:
                            manual_generate_option = self.objPage.locator(selectors["manual_generate_option"])
                            manual_generate_option.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                            manual_generate_option.click()
                            logger.info(f"Clicked 'Manual generate once' button ({self.browser_type}).")
                            break
                        except TimeoutError:
                            logger.error(f"'Manual generate once' option not found for {account_name} on attempt {retry + 1} ({self.browser_type}).")
                            retry += 1
                            if retry < max_retries:
                                logger.info(f"Retrying manual generate option for {account_name} ({self.browser_type})...")
                                self.objPage.wait_for_timeout(2000)
                            continue
                    if retry >= max_retries:
                        logger.error(f"Failed to click 'Manual generate once' for {account_name} after {max_retries} attempts ({self.browser_type}).")
                        failed_accounts.append(account_name)
                        self.download_stats.append({
                            "store": account_name,
                            "status": "Failed (Manual Generate Option Not Found)",
                            "from_date": from_date_val,
                            "to_date": to_date_val,
                            "file_path": None,
                            "time_taken": None
                        })
                        continue

                    try:
                        self.objPage.wait_for_selector(selectors["date_range_selector"], state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                        logger.info(f"Pop-up with date range selector detected ({self.browser_type}).")
                        date_range_selector = self.objPage.locator(selectors["date_range_selector"])
                        date_range_selector.click()
                        logger.info(f"Clicked Date Range selector to open calendar ({self.browser_type}).")
                        self.objPage.wait_for_load_state("domcontentloaded", timeout=10000)

                        from_date_input = self.objPage.locator(selectors["from_date_input"]).first
                        from_date_input.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                        from_date_input.fill(from_date_val)
                        logger.info(f"Filled From Date with: {from_date_val} ({self.browser_type})")

                        to_date_input = self.objPage.locator(selectors["to_date_input"]).nth(1)
                        to_date_input.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                        to_date_input.fill(to_date_val)
                        logger.info(f"Filled To Date with: {to_date_val} ({self.browser_type})")

                        submit_button = self.objPage.locator(selectors["submit_button"])
                        submit_button.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                        submit_button.click()
                        logger.info(f"Clicked Submit button ({self.browser_type}).")
                        self.objPage.wait_for_load_state("networkidle", timeout=35000 if self.browser_type == "firefox" else 30000)

                        radio_button_1 = self.objPage.locator(selectors["radio_button_1"]).first
                        radio_button_1.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                        radio_button_1.click()
                        logger.info(f"Selected first radio button ({self.browser_type}).")

                        radio_button_2 = self.objPage.locator(selectors["radio_button_2"]).nth(1)
                        radio_button_2.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                        radio_button_2.click()
                        logger.info(f"Selected second radio button ({self.browser_type}).")

                        file_type_dropdown = self.objPage.locator(selectors["file_type_dropdown"])
                        file_type_dropdown.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                        options = file_type_dropdown.locator("option").all()
                        option_values = [option.get_attribute("value") for option in options]
                        logger.info(f"Available file type options: {option_values} ({self.browser_type})")
                        if "XLS_GEN" in option_values:
                            file_type_dropdown.select_option(value="XLS_GEN")
                            logger.info(f"Selected File type option: XLS_GEN ({self.browser_type})")
                        else:
                            logger.error(f"Option 'XLS_GEN' not found in file type dropdown for {account_name}. Available options: {option_values} ({self.browser_type})")
                            failed_accounts.append(account_name)
                            self.download_stats.append({
                                "store": account_name,
                                "status": "Failed (File Type Option Not Found)",
                                "from_date": from_date_val,
                                "to_date": to_date_val,
                                "file_path": None,
                                "time_taken": None
                            })
                            continue

                        generate_report_button = self.objPage.locator(selectors["generate_report_button"])
                        generate_report_button.wait_for(state="visible", timeout=35000 if self.browser_type == "firefox" else 30000)
                        generate_report_button.click()
                        logger.info(f"Clicked Generate report button ({self.browser_type}).")

                        self.objPage.wait_for_timeout(60000)
                        logger.info(f"Waited 60 seconds for report generation ({self.browser_type}).")

                        gmt_plus_530 = timezone(timedelta(hours=5, minutes=30))
                        current_time = datetime.now(gmt_plus_530)
                        time_window = current_time - timedelta(minutes=10)
                        logger.info(f"Current time (GMT+5:30): {current_time.strftime('%b %d, %Y, %H:%M:%S %z')} ({self.browser_type})")
                        logger.info(f"Time window start (GMT+5:30): {time_window.strftime('%b %d, %Y, %H:%M:%S %z')} ({self.browser_type})")

                        rows = self.objPage.locator("table tbody tr").all()
                        logger.info(f"Found {len(rows)} rows in Generated reports table ({self.browser_type}).")
                        for i, row in enumerate(rows):
                            try:
                                generated_on = row.locator("td:nth-child(3)").inner_text()
                                logger.info(f"Row {i+1} Generated on: {generated_on} ({self.browser_type})")
                            except Exception as e:
                                logger.error(f"Error reading Generated on for row {i+1}: {str(e)} ({self.browser_type})")

                        latest_row = None
                        for row in rows:
                            try:
                                generated_on = row.locator("td:nth-child(3)").inner_text()
                                generated_on = generated_on.replace("GMT+5:30", "+0530")
                                report_time = datetime.strptime(generated_on, "%b %d, %Y, %H:%M:%S %z")
                                if time_window <= report_time <= current_time:
                                    latest_row = row
                                    logger.info(f"Found matching report with Generated on: {generated_on} ({self.browser_type})")
                                    break
                            except (ValueError, IndexError) as e:
                                logger.error(f"Error parsing Generated on in row: {str(e)} ({self.browser_type})")
                                continue

                        if latest_row:
                            try:
                                td_content = latest_row.locator("td:nth-child(5)").inner_html()
                                td_classes = latest_row.locator("td:nth-child(5)").get_attribute("class")
                                logger.info(f"5th td classes: {td_classes} ({self.browser_type})")
                                logger.info(f"5th td content: {td_content} ({self.browser_type})")
                            except Exception as e:
                                logger.error(f"Error reading 5th td: {str(e)} ({self.browser_type})")

                            download_icon = latest_row.locator(selectors["download_icon"])
                            icon_count = download_icon.count()
                            logger.info(f"Download icon count in latest row: {icon_count} ({self.browser_type})")
                            if icon_count == 0:
                                logger.error(f"No download icon found in latest row for {account_name} ({self.browser_type}).")
                                failed_accounts.append(account_name)
                                self.download_stats.append({
                                    "store": account_name,
                                    "status": "Failed (Download Icon Not Found)",
                                    "from_date": from_date_val,
                                    "to_date": to_date_val,
                                    "file_path": None,
                                    "time_taken": None
                                })
                                continue

                            try:
                                icon_class = download_icon.get_attribute("class")
                                logger.info(f"Download icon class: {icon_class} ({self.browser_type})")
                            except Exception as e:
                                logger.error(f"Error reading download icon attributes: {str(e)} ({self.browser_type})")

                            try:
                                download_icon.wait_for(state="visible", timeout=70000 if self.browser_type == "firefox" else 60000)
                                logger.info(f"Download icon is visible ({self.browser_type}).")
                                with self.objPage.expect_download(timeout=70000 if self.browser_type == "firefox" else 60000) as download_info:
                                    download_icon.first.click()
                                    logger.info(f"Clicked download icon for latest report ({self.browser_type}).")
                                download = download_info.value
                                download_path = os.path.join(download_dir, f"{account_name}_report.xlsx")
                                download.save_as(download_path)
                                time_taken = time.time() - start_time
                                logger.info(f"Downloaded report for {account_name} to {download_path} in {time_taken:.2f} seconds ({self.browser_type})")
                                self.downloaded_files.append(download_path)
                                self.download_stats.append({
                                    "store": account_name,
                                    "status": "Success",
                                    "from_date": from_date_val,
                                    "to_date": to_date_val,
                                    "file_path": download_path,
                                    "time_taken": time_taken
                                })
                            except TimeoutError:
                                logger.error(f"Download icon not visible within timeout for {account_name} ({self.browser_type}).")
                                failed_accounts.append(account_name)
                                self.download_stats.append({
                                    "store": account_name,
                                    "status": "Failed (Download Timeout)",
                                    "from_date": from_date_val,
                                    "to_date": to_date_val,
                                    "file_path": None,
                                    "time_taken": None
                                })
                                continue

                        else:
                            logger.error(f"No report found within 10 minutes of current time in GMT+5:30 for {account_name} ({self.browser_type}).")
                            failed_accounts.append(account_name)
                            self.download_stats.append({
                                "store": account_name,
                                "status": "Failed (No Recent Report)",
                                "from_date": from_date_val,
                                "to_date": to_date_val,
                                "file_path": None,
                                "time_taken": None
                            })
                            continue

                    except TimeoutError as e:
                        logger.error(f"Error in pop-up interaction for {account_name}: {str(e)} ({self.browser_type})")
                        failed_accounts.append(account_name)
                        self.download_stats.append({
                            "store": account_name,
                            "status": "Failed (Pop-up Interaction Error)",
                            "from_date": from_date_val,
                            "to_date": to_date_val,
                            "file_path": None,
                            "time_taken": None
                        })
                        continue

                except Exception as e:
                    logger.error(f"Unexpected error processing account {account_name}: {str(e)} ({self.browser_type})", exc_info=True)
                    failed_accounts.append(account_name)
                    self.download_stats.append({
                        "store": account_name,
                        "status": f"Failed ({str(e)})",
                        "from_date": from_date_val,
                        "to_date": to_date_val,
                        "file_path": None,
                        "time_taken": None
                    })
                    continue

            if failed_accounts:
                error_message = f"Failed to process accounts: {', '.join(failed_accounts)}"
                logger.error(error_message)
                self.showErrorPopUp(error_message)
                return False
            return True

        except FileNotFoundError as e:
            logger.error(f"{str(e)} ({self.browser_type})", exc_info=True)
            self.showErrorPopUp(str(e))
            raise
        except Exception as e:
            logger.error(f"Error during XLSX search process: {str(e)} ({self.browser_type})", exc_info=True)
            self.showErrorPopUp("Error during report generation process")
            raise

def setup_playwright_driver(driver_path, browser_type="chromium"):
    """Download Playwright browser drivers to a specific directory."""
    os.makedirs(driver_path, exist_ok=True)
    os.environ["PLAYWRIGHT_BROWSERS_PATH"] = driver_path
    with sync_playwright() as p:
        if browser_type.lower() == "firefox":
            p.firefox.launch()
            logger.info(f"Playwright Firefox drivers downloaded to: {driver_path}")
        else:
            p.chromium.launch()
            logger.info(f"Playwright Chromium drivers downloaded to: {driver_path}")
    return driver_path

def get_exe_directory():
    """Return the directory of the executable or script."""
    if getattr(sys, 'frozen', False):
        return os.path.dirname(sys.executable)
    return os.path.dirname(os.path.abspath(__file__))

def get_download_directory(download_dir_arg=None):
    """Return the download directory, creating a date-based subfolder."""
    base_dir = download_dir_arg or os.path.join(get_exe_directory(), "downloads")
    date_str = datetime.now().strftime("%Y%m%d")
    download_dir = os.path.join(base_dir, date_str)
    os.makedirs(download_dir, exist_ok=True)
    logger.info(f"Using download directory: {download_dir}")
    return download_dir

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Adyen Downloader Automation Script')
    parser.add_argument('--site-url', type=str, help='Login page URL')
    parser.add_argument('--username', type=str, help='Username for login')
    parser.add_argument('--password', type=str, help='Password for login')
    parser.add_argument('--driver-dir', type=str, help='Directory for Playwright drivers')
    parser.add_argument('--headless', type=bool, default=False, help='Run in headless mode')
    parser.add_argument('--browser-args', type=str, nargs='*', help='Custom browser arguments')
    parser.add_argument('--browser-type', type=str, default='chromium', choices=['chromium', 'firefox'], help='Browser type to use (chromium or firefox, default: chromium)')
    parser.add_argument('--state-file', type=str, default='state.json', help='Path to storage state file')
    parser.add_argument('--xlsx-file', type=str, default='stores.xlsx', help='Path to XLSX file with account data')
    parser.add_argument('--from-date', type=str, help='Start date for report (YYYY-MM-DD), defaults to previous month first day')
    parser.add_argument('--to-date', type=str, help='End date for report (YYYY-MM-DD), defaults to previous month last day')
    parser.add_argument('--email-config', type=str, help='Path to email config JSON file')
    parser.add_argument('--download-dir', type=str, help='Base directory for downloads (date-based subfolder will be created)')
    return parser.parse_args()

def extract_dashboard_url(site_url):
    """Extract the dashboard URL from the site_url's returnUrl parameter."""
    try:
        parsed_url = urllib.parse.urlparse(site_url)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        return_url = query_params.get('returnUrl', [None])[0]
        if return_url:
            logger.info(f"Extracted dashboard URL: {return_url}")
            return return_url
        logger.warning("No returnUrl found in site_url. Using site_url as fallback.")
        return site_url
    except Exception as e:
        logger.error(f"Error extracting dashboard URL: {str(e)}")
        return site_url

def is_session_valid(page, site_url, browser_type, selectors):
    """Check if the stored session is valid by navigating to the dashboard URL."""
    dashboard_url = extract_dashboard_url(site_url)
    max_retries = 5
    attempt = 0
    
    while attempt < max_retries:
        try:
            page.goto(dashboard_url)
            logger.info(f"Navigated to dashboard URL: {dashboard_url} (attempt {attempt + 1}) ({browser_type})")
            logger.info(f"Current URL after navigation: {page.url} ({browser_type})")
            logger.info(f"Page title: {page.title()} ({browser_type})")

            max_attempts = 60
            sub_attempt = 0
            while sub_attempt < max_attempts:
                try:
                    logger.info(f"Dashboard check attempt {sub_attempt + 1}. URL: {page.url} ({browser_type})")
                    dashboard_element = page.locator(selectors["dropdown"])
                    count = dashboard_element.count()
                    logger.info(f"Dropdown selector count: {count}, Selector: {selectors['dropdown']} ({browser_type})")
                    if count > 0:
                        dashboard_element.wait_for(state="visible", timeout=1000)
                        logger.info(f"Stored session is valid: Dashboard element found ({browser_type}).")
                        return True
                    if "essentials-live.adyen.com" in page.url.lower():
                        logger.info(f"Stored session is valid: Dashboard URL confirmed: {page.url} ({browser_type})")
                        return True
                    logger.warning(f"Dashboard element not yet visible ({browser_type}).")
                    page.wait_for_timeout(2000)
                    sub_attempt += 1
                except Exception as e:
                    logger.warning(f"Dashboard check attempt {sub_attempt + 1} failed: {str(e)} ({browser_type})")
                    if "essentials-live.adyen.com" in page.url.lower():
                        logger.info(f"Stored session is valid: Dashboard URL confirmed: {page.url} ({browser_type})")
                        return True
                    logger.warning(f"Retrying dashboard check ({browser_type}).")
                    page.wait_for_timeout(2000)
                    sub_attempt += 1

            if page.locator(selectors["username_field"]).count() > 0 or "authn-live.adyen.com" in page.url.lower():
                logger.info(f"Stored session is invalid: Redirected to login page ({browser_type}).")
                if os.path.exists(f"state_{browser_type}.json"):
                    os.remove(f"state_{browser_type}.json")
                    logger.info(f"Deleted invalid state_{browser_type}.json to force fresh login.")
                return False
            logger.warning(f"Timeout: Dashboard element not found after waiting ({browser_type}).")
            attempt += 1
            if attempt < max_retries:
                logger.info(f"Retrying session validation by navigating to {dashboard_url} ({browser_type})...")
                page.goto(dashboard_url)
                page.wait_for_timeout(3000)
            continue

        except Exception as e:
            logger.error(f"Error checking session validity on attempt {attempt + 1}: {str(e)} ({browser_type})")
            attempt += 1
            if attempt < max_retries:
                logger.info(f"Retrying session validation by navigating to {dashboard_url} ({browser_type})...")
                page.goto(dashboard_url)
                page.wait_for_timeout(3000)
            continue

    if page.locator(selectors["username_field"]).count() > 0 or "authn-live.adyen.com" in page.url.lower():
        logger.info(f"Stored session is invalid: Redirected to login page ({browser_type}).")
        if os.path.exists(f"state_{browser_type}.json"):
            os.remove(f"state_{browser_type}.json")
            logger.info(f"Deleted invalid state_{browser_type}.json to force fresh login.")
        return False
    logger.error(f"Failed to validate session after retries ({browser_type}).")
    return False

def handle_full_login(navigator, playwright_helper, site_url, username, password, xlsx_file, download_dir, from_date, to_date, state_file, selectors):
    """Handle Case 1: Full login with OTP when no state.json or session is invalid."""
    try:
        logger.info(f"Initiating full login process ({playwright_helper.browser_type})...")
        playwright_helper.login(
            url=site_url,
            username=username,
            password=password,
            selectors=selectors
        )
        success = navigator.handleOTPProcess(selectors)
        if success:
            logger.info(f"Login automation completed successfully ({playwright_helper.browser_type}).")
            playwright_helper.save_storage_state(state_file)
            navigator.showStartProcessPopUp()
            logger.info(f"Validating dashboard after start pop-up. URL: {navigator.objPage.url} ({playwright_helper.browser_type})")
            if not navigator.validate_dashboard(selectors):
                logger.error(f"Dashboard validation failed after start pop-up ({playwright_helper.browser_type}).")
                navigator.showErrorPopUp("Failed to validate dashboard after login")
                return False
            success = navigator.perform_search_from_xlsx(xlsx_file, download_dir, from_date, to_date, selectors)
            if success:
                return True
            else:
                logger.error(f"Report generation failed ({playwright_helper.browser_type}).")
                navigator.showErrorPopUp("Report generation failed")
                return False
        else:
            logger.error(f"Login automation failed ({playwright_helper.browser_type}).")
            navigator.showErrorPopUp("Login failed")
            return False
    except Exception as e:
        logger.error(f"Error in full login process: {str(e)} ({playwright_helper.browser_type})", exc_info=True)
        navigator.showErrorPopUp("Error during login process")
        return False

def handle_session_validation(navigator, playwright_helper, site_url, username, password, xlsx_file, download_dir, from_date, to_date, state_file, selectors):
    """Handle Case 2: Validate session with state.json, fall back to full login if invalid."""
    try:
        is_valid = is_session_valid(playwright_helper.objPage, site_url, playwright_helper.browser_type, selectors)
        if is_valid:
            logger.info(f"Using stored authentication state. Skipping login and OTP ({playwright_helper.browser_type}).")
            navigator.showStartProcessPopUp()
            logger.info(f"Validating dashboard after start pop-up. URL: {navigator.objPage.url} ({playwright_helper.browser_type})")
            if not navigator.validate_dashboard(selectors):
                logger.error(f"Dashboard validation failed after start pop-up ({playwright_helper.browser_type}).")
                navigator.showErrorPopUp("Failed to validate dashboard after session validation")
                return False
            success = navigator.perform_search_from_xlsx(xlsx_file, download_dir, from_date, to_date, selectors)
            if success:
                return True
            else:
                logger.error(f"Report generation failed ({playwright_helper.browser_type}).")
                navigator.showErrorPopUp("Report generation failed")
                return False
        else:
            logger.warning(f"Stored session invalid or expired. Falling back to full login ({playwright_helper.browser_type}).")
            return handle_full_login(navigator, playwright_helper, site_url, username, password, xlsx_file, download_dir, from_date, to_date, state_file, selectors)
    except Exception as e:
        logger.error(f"Error in session validation process: {str(e)} ({playwright_helper.browser_type})", exc_info=True)
        navigator.showErrorPopUp("Error during session validation")
        return False

def send_notification_email(success, downloaded_files, download_stats, total_time, browser_type, email_config, username):
    """Send email notification with automation statistics in a formatted HTML table."""
    status = "Success" if success else "Failed"
    manual_time_minutes = 120  # 2 hours for 110 stores
    total_time_minutes = total_time / 60
    time_saved_minutes = manual_time_minutes - total_time_minutes
    avg_time_per_store = sum(stat["time_taken"] for stat in download_stats if stat["time_taken"]) / len([stat for stat in download_stats if stat["time_taken"]]) if any(stat["time_taken"] for stat in download_stats) else 0

    html_content = f"""
    <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; color: #333; margin: 0; padding: 20px; }}
                h2 {{ color: #1a73e8; }}
                table {{ border-collapse: collapse; width: 100%; max-width: 800px; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; color: #333; }}
                tr:nth-child(even) {{ background-color: #f9f9f9; }}
                .status-success {{ color: #2e7d32; font-weight: bold; }}
                .status-failed {{ color: #d32f2f; font-weight: bold; }}
                .summary {{ margin-bottom: 20px; }}
                .summary p {{ margin: 5px 0; }}
            </style>
        </head>
        <body>
            <h2>Adyen Downloader Automation Report</h2>
            <div class="summary">
                <p><strong>Status:</strong> {status}</p>
                <p><strong>User:</strong> {username}</p>
                <p><strong>Browser Used:</strong> {browser_type}</p>
                <p><strong>Total Time Taken:</strong> {total_time:.2f} seconds ({total_time_minutes:.2f} minutes)</p>
                <p><strong>Average Time per Store:</strong> {avg_time_per_store:.2f} seconds</p>
                <p><strong>Total Time Saved:</strong> {time_saved_minutes:.2f} minutes</p>
                <p><strong>Total Files Downloaded:</strong> {len(downloaded_files)}</p>
                <p><strong>Download Directory:</strong> {os.path.dirname(downloaded_files[0]) if downloaded_files else 'N/A'}</p>
            </div>
            <table>
                <tr>
                    <th>Store</th>
                    <th>Status</th>
                    <th>From Date</th>
                    <th>To Date</th>
                    <th>File Location</th>
                    <th>Time Taken (s)</th>
                </tr>
                {"".join([f'<tr><td>{stat["store"]}</td><td class="status-{"success" if stat["status"] == "Success" else "failed"}">{stat["status"]}</td><td>{stat["from_date"]}</td><td>{stat["to_date"]}</td><td>{stat["file_path"] if stat["file_path"] else "N/A"}</td><td>{stat["time_taken"]:.2f if stat["time_taken"] else "N/A"}</td></tr>' for stat in download_stats])}
            </table>
            <p>Please find the log file attached for detailed information.</p>
            <p>Thank you for using Adyen Downloader.</p>
        </body>
    </html>
    """
    try:
        send_email(
            from_address=email_config["from_address"],
            password=email_config["password"],
            to_addresses=email_config["to_addresses"],
            cc_addresses=email_config["cc_addresses"],
            subject=f"Adyen Downloader: {status}",
            html_content=html_content,
            smtp_server=email_config["smtp_server"],
            smtp_port=email_config["smtp_port"],
            lsAttachmentPath=[LOG_FILE]
        )
    except Exception as e:
        logger.error(f"Failed to send notification email: {str(e)}")

def main():
    start_time = time.time()
    args = parse_args()
    logger.info(f"Starting Adyen Downloader automation with args: {vars(args)}")

    username = os.getlogin()  # Fetch username from os module
    logger.info(f"Automation initiated by user: {username}")

    exe_dir = get_exe_directory()
    try:
        selectors = load_selectors(os.path.join(exe_dir, "resources", "selectors.json"))
        email_config_path = args.email_config or os.path.join(exe_dir, "resources", "email_config.json")
        email_config = load_email_config(email_config_path)
        download_dir = get_download_directory(args.download_dir)
    except Exception as e:
        logger.error(f"Failed to load configuration: {str(e)}")
        if 'navigator' in locals():
            navigator.showErrorPopUp("Failed to load configuration files")
        total_time = time.time() - start_time
        send_notification_email(False, [], [], total_time, "unknown", email_config, username)
        sys.exit(1)

    site_url = args.site_url or 'https://authn-live.adyen.com/authn/ui/login?request=eyJBdXRoblJlcXVlc3QiOnsiYWN0aXZpdHlHcm91cCI6IkJPX0NBIiwiY3JlZHNSZWFzb24iOlsiTG9nZ2luZyBpbiB0byBhcHBsaWNhdGlvbiBlc3NlbnRpYWxzIl0sImZvcmNlTmV3U2Vzc2lvbiI6ImZhbHNlIiwiZm9yZ290UGFzc3dvcmRVcmwiOiJodHRwczpcL1wvZXNzZW50aWFscy1saXZlLmFkeWVuLmNvbVwvZXNzZW50aWFsc1wvbG9iYnlcL3Bhc3N3b3JkLXJlc2V0XC9mb3Jnb3QtcGFzc3dvcmQiLCJyZXF1ZXN0VGltZSI6IjIwMjUtMDctMDNUMDg6MDk6MDkrMDI6MDAiLCJyZXF1ZXN0ZWRDcmVkZW50aWFscyI6W3siUmVxdWVzdGVkQ3JlZGVudGlhbCI6eyJhY2NlcHRlZEFjdGl2aXR5IjpbeyJBY2NlcHRlZEFjdGl2aXR5Ijp7ImFjdGl2aXR5R3JvdXAiOiJCT19DQSIsImFjdGl2aXR5VHlwZSI6IklNUExJQ0lUIiwibWlsbGlzZWNvbmRzQWdvIjo5MDAwMDB9fV0sInR5cGUiOiJQQVNTV09SRCJ9fSx7IlJlcXVlc3RlZENyZWRlbnRpYWwiOnsiYWNjZXB0ZWRBY3Rpdml0eSI6W3siQWNjZXB0ZWRBY3Rpdml0eSI6eyJhY3Rpdml0eUdyb3VwIjoiQk9fQ0EiLCJhY3Rpdml0eVR5cGUiOiJHUkFDRV9DT09LSUUiLCJtaWxsaXNlY29uZHNBZ28iOjB9fV0sInR5cGUiOiJUV09fRkFDVE9SIn19XSwicmVxdWVzdGluZ0FwcCI6ImVzc2VudGlhbHMiLCJyZXR1cm5VcmwiOiJodHRwczpcL1wvZXNzZW50aWFscy1saXZlLmFkeWVuLmNvbVwvZXNzZW50aWFsc1wvaG9tZT9hY2NvdW50S2V5PVMzQi1Wbmh2TGpwN2JWVmhlQ2hpT1g4Z1pWczMzYXloMzRMazBsWW04OFVWZENTMUl6ZFVCN09nIiwic2lnbmF0dXJlIjoiVjAwM1NsN050UEptYmVzQlBZdlNMYWFHclQ0ZXRqRUVoSVRoZ0NZWVRKOVJZZ1c4PSJ9fQ%3D%3D'
    username_arg = args.username or '<EMAIL>'
    password = args.password or 'Sunshine123!'
    driver_dir = args.driver_dir or os.path.join(exe_dir, "playwrightDrivers")
    headless = args.headless
    browser_args = args.browser_args or ["--start-maximized"]
    browser_type = args.browser_type  # Use user-specified browser type
    xlsx_file = args.xlsx_file or os.path.join(exe_dir, "stores.xlsx")
    from_date = args.from_date
    to_date = args.to_date

    success = False
    try:
        logger.info(f"Attempting automation with {browser_type}...")
        state_file = os.path.join(exe_dir, f"state_{browser_type}.json")
        setup_playwright_driver(driver_path=driver_dir, browser_type=browser_type)
        storage_state = state_file if os.path.exists(state_file) else None
        playwright_helper = PlaywrightHelper(bHeadless=headless, storage_state=storage_state, browser_type=browser_type)
        
        logger.info(f"Launching {browser_type} browser...")
        playwright_helper.start_browser(browser_args=browser_args)

        navigator = CWebNavigator(playwright_helper)
        try:
            if storage_state:
                success = handle_session_validation(
                    navigator=navigator,
                    playwright_helper=playwright_helper,
                    site_url=site_url,
                    username=username_arg,
                    password=password,
                    xlsx_file=xlsx_file,
                    download_dir=download_dir,
                    from_date=from_date,
                    to_date=to_date,
                    state_file=state_file,
                    selectors=selectors
                )
            else:
                success = handle_full_login(
                    navigator=navigator,
                    playwright_helper=playwright_helper,
                    site_url=site_url,
                    username=username_arg,
                    password=password,
                    xlsx_file=xlsx_file,
                    download_dir=download_dir,
                    from_date=from_date,
                    to_date=to_date,
                    state_file=state_file,
                    selectors=selectors
                )

            total_time = time.time() - start_time
            manual_time_minutes = 120  # 2 hours for 110 stores
            time_saved_minutes = manual_time_minutes - (total_time / 60)

            if success:
                logger.info(f"Automation completed successfully with {browser_type}.")
                navigator.showEndProcessPopUp(navigator.download_stats, total_time, time_saved_minutes)
            else:
                logger.error(f"Automation failed with {browser_type}. Exiting...")
                navigator.showErrorPopUp(f"Automation failed with {browser_type}. Please check logs and rerun.")
                send_notification_email(success, navigator.downloaded_files, navigator.download_stats, total_time, browser_type, email_config, username)
                sys.exit(1)

        except Exception as e:
            logger.error(f"Error in automation with {browser_type}: {str(e)}", exc_info=True)
            navigator.showErrorPopUp(f"Error in automation with {browser_type}. Please check logs and rerun.")
            total_time = time.time() - start_time
            send_notification_email(False, navigator.downloaded_files, navigator.download_stats, total_time, browser_type, email_config, username)
            sys.exit(1)
        finally:
            playwright_helper.close()

    except Exception as e:
        logger.error(f"Error in main execution: {str(e)}", exc_info=True)
        if 'navigator' in locals():
            navigator.showErrorPopUp("Error in automation setup. Please check logs and rerun.")
        total_time = time.time() - start_time
        send_notification_email(False, [], [], total_time, browser_type, email_config, username)
        sys.exit(1)
    finally:
        if 'playwright_helper' in locals():
            playwright_helper.close()

if __name__ == "__main__":
    main()