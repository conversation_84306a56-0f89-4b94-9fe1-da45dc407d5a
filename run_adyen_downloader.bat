@echo off
setlocal EnableDelayedExpansion

echo Adyen Downloader Runner
echo ======================
echo This script will help you run the Adyen Downloader executable with the necessary arguments.
echo Press Enter to use default values where applicable, or input your own values.
echo.

:: Check if the executable exists
if not exist "dist\AdyenDownloader.exe" (
    echo ERROR: 'dist\AdyenDownloader.exe' not found.
    echo Please run 'create_exe.bat' first to create the executable.
    pause
    exit /b 1
)

:: Initialize variables
set CMD=dist\AdyenDownloader.exe

:: Prompt for site_url
set /p SITE_URL=Enter site URL (press Enter for default: https://authn-live.adyen.com/...): 
if "!SITE_URL!"=="" (
    set SITE_URL=https://authn-live.adyen.com/authn/ui/login?request=eyJBdXRoblJlcXVlc3QiOnsiYWN0aXZpdHlHcm91cCI6IkJPX0NBIiwiY3JlZHNSZWFzb24iOlsiTG9nZ2luZyBpbiB0byBhcHBsaWNhdGlvbiBlc3NlbnRpYWxzIl0sImZvcmNlTmV3U2Vzc2lvbiI6ImZhbHNlIiwiZm9yZ29tUGFzc3dvcmRVcmwiOiJodHRwczpcL1wvZXNzZW50aWFscy1saXZlLmFkeWVuLmNvbVwvZXNzZW50aWFsc1wvbG9iYnlcL3Bhc3N3b3JkLXJlc2V0XC9mb3Jnb3QtcGFzc3dvcmQiLCJyZXF1ZXN0VGltZSI6IjIwMjUtMDctMDNUMDg6MDk6MDkrMDI6MDAiLCJyZXF1ZXN0ZWRDcmVkZW50aWFscyI6W3siUmVxdWVzdGVkQ3JlZGVudGlhbCI6eyJhY2NlcHRlZEFjdGl2aXR5IjpbeyJBY2NlcHRlZEFjdGl2aXR5Ijp7ImFjdGl2aXR5R3JvdXAiOiJCT19DQSIsImFjdGl2aXR5VHlwZSI6IklNUExJQ0lUIiwibWlsbGlzZWNvbmRzQWdvIjo5MDAwMDB9fV0sInR5cGUiOiJQQVNTV09SRCJ9fSx7IlJlcXVlc3RlZENyZWRlbnRpYWwiOnsiYWNjZXB0ZWRBY3Rpdml0eSI6W3siQWNjZXB0ZWRBY3Rpdml0eSI6eyJhY3Rpdml0eUdyb3VwIjoiQk9fQ0EiLCJhY3Rpdml0eVR5cGUiOiJHUkFDRV9DT09LSUUiLCJtaWxsaXNlY29uZHNBZ28iOjB9fV0sInR5cGUiOiJUV09fRkFDVE9SIn19XSwicmVxdWVzdGluZ0FwcCI6ImVzc2VudGlhbHMiLCJyZXR1cm5VcmwiOiJodHRwczpcL1wvZXNzZW50aWFscy1saXZlLmFkeWVuLmNvbVwvZXNzZW50aWFsc1wvaG9tZT9hY2NvdW50S2V5PVMzQi1Wbmh2TGpwN2JWVmhlQ2hpT1g4Z1pWczMzYXloMzRMazBsWW04OFVWZENTMUl6ZFVCN09nIiwic2lnbmF0dXJlIjoiVjAwM1NsN050UEptYmVzQlBZdlNMYWFHclQ0ZXRqRUVoSVRoZ0NZWVRKOVJZZ1c4PSJ9fQ^%^3D^%^3D
)
set CMD=!CMD! --site-url "!SITE_URL!"

:: Prompt for username
set /p USERNAME=Enter username (press Enter to use current system user: %USERNAME%): 
if not "!USERNAME!"=="" (
    set CMD=!CMD! --username "!USERNAME!"
)

:: Prompt for password
set /p PASSWORD=Enter password (press Enter for default: Sunshine123!): 
if "!PASSWORD!"=="" (
    set PASSWORD=Sunshine123!
)
set CMD=!CMD! --password "!PASSWORD!"

:: Prompt for browser-type
echo Valid browser types: chromium, firefox
set /p BROWSER_TYPE=Enter browser type (press Enter for default: chromium): 
if "!BROWSER_TYPE!"=="" (
    set BROWSER_TYPE=chromium
)
if /i "!BROWSER_TYPE!"=="chromium" (
    set CMD=!CMD! --browser-type chromium
) else if /i "!BROWSER_TYPE!"=="firefox" (
    set CMD=!CMD! --browser-type firefox
) else (
    echo ERROR: Invalid browser type '!BROWSER_TYPE!'. Must be 'chromium' or 'firefox'.
    pause
    exit /b 1
)

:: Prompt for xlsx-file
set /p XLSX_FILE=Enter path to stores.xlsx (press Enter for default: stores.xlsx): 
if "!XLSX_FILE!"=="" (
    set XLSX_FILE=stores.xlsx
)
if exist "!XLSX_FILE!" (
    set CMD=!CMD! --xlsx-file "!XLSX_FILE!"
) else (
    echo WARNING: XLSX file '!XLSX_FILE!' not found. Proceeding with default if it exists in executable directory.
    set CMD=!CMD! --xlsx-file "!XLSX_FILE!"
)

:: Prompt for from-date
set /p FROM_DATE=Enter from date (YYYY-MM-DD, press Enter to use previous month): 
if not "!FROM_DATE!"=="" (
    set CMD=!CMD! --from-date "!FROM_DATE!"
)

:: Prompt for to-date
set /p TO_DATE=Enter to date (YYYY-MM-DD, press Enter to use previous month): 
if not "!TO_DATE!"=="" (
    set CMD=!CMD! --to-date "!TO_DATE!"
)

:: Prompt for download-dir
set /p DOWNLOAD_DIR=Enter download directory (press Enter for default: downloads): 
if not "!DOWNLOAD_DIR!"=="" (
    set CMD=!CMD! --download-dir "!DOWNLOAD_DIR!"
)

:: Prompt for headless mode
set /p HEADLESS=Run in headless mode? (y/n, press Enter for default: n): 
if /i "!HEADLESS!"=="y" (
    set CMD=!CMD! --headless true
) else (
    set CMD=!CMD! --headless false
)

:: Prompt for email-config
set /p EMAIL_CONFIG=Enter path to email_config.json (press Enter for default: resources\email_config.json): 
if "!EMAIL_CONFIG!"=="" (
    set EMAIL_CONFIG=resources\email_config.json
)
set CMD=!CMD! --email-config "!EMAIL_CONFIG!"

:: Display the final command
echo.
echo Running the following command:
echo !CMD!
echo.

:: Execute the command
!CMD!
if %ERRORLEVEL% neq 0 (
    echo ERROR: Adyen Downloader failed with error code %ERRORLEVEL%.
    echo Check the logs in the 'logs' directory for details and try running again.
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo Adyen Downloader completed successfully.
echo Check the 'downloads' directory for reports and the 'logs' directory for the log file.
pause
exit /b 0