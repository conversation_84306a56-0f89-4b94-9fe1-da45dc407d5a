@echo off
REM 1. Create virtual environment
python -m venv venv

REM 2. Activate virtual environment
call venv\Scripts\activate.bat

REM 3. Set browser path to current directory and install drivers
set PLAYWRIGHT_BROWSERS_PATH=%CD%\playwright-browsers

REM 4. Upgrade pip and install requirements
echo Installing Playwright and dependencies...
python -m pip install --upgrade pip
pip install -r requirements.txt

echo.
echo ✅ Playwright setup complete with browsers installed to:
echo   %PLAYWRIGHT_BROWSERS_PATH%
pause
