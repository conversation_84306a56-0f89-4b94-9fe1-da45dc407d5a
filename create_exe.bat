@echo off
setlocal EnableDelayedExpansion

echo Creating Adyen Downloader executable...
echo.

call venv\Scripts\activate.bat

:: Create a temporary directory for Playwright drivers
set DRIVER_DIR=playwrightDrivers

:: Set Playwright browser binaries path to the current directory's playwrightDrivers folder
set PLAYWRIGHT_BROWSERS_PATH=%CD%\playwrightDrivers

:: Run PyInstaller to create the executable
echo Creating executable with PyInstaller...
pyinstaller ^
    --add-data "resources;resources" ^
    --add-data "%DRIVER_DIR%;playwrightDrivers" ^
    --name AdyenDownloader ^
    --noconfirm ^
    main.py
if %ERRORLEVEL% neq 0 (
    echo ERROR: PyInstaller failed to create the executable.
    pause
    exit /b 1
)

:: Verify the executable was created
if not exist "dist\AdyenDownloader.exe" (
    echo ERROR: Executable 'dist\AdyenDownloader.exe' was not created.
    pause
    exit /b 1
)

echo.
echo Success: AdyenDownloader.exe has been created in the 'dist' directory.
echo The executable includes the 'resources' directory and Playwright drivers.
echo You can now use 'run_adyen_downloader.bat' to run the application.
pause
exit /b 0